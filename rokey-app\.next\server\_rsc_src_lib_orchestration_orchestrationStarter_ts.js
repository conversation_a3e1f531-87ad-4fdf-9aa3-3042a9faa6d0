"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_rsc_src_lib_orchestration_orchestrationStarter_ts";
exports.ids = ["_rsc_src_lib_orchestration_orchestrationStarter_ts"];
exports.modules = {

/***/ "(rsc)/./src/lib/orchestration/orchestrationStarter.ts":
/*!*******************************************************!*\
  !*** ./src/lib/orchestration/orchestrationStarter.ts ***!
  \*******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   startOrchestrationExecution: () => (/* binding */ startOrchestrationExecution)\n/* harmony export */ });\n/* harmony import */ var _lib_supabase_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/supabase/server */ \"(rsc)/./src/lib/supabase/server.ts\");\n/* harmony import */ var _utils_orchestrationUtils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/utils/orchestrationUtils */ \"(rsc)/./src/utils/orchestrationUtils.ts\");\n\n\nasync function startOrchestrationExecution(executionId) {\n    console.log(`[Orchestration Starter] Starting execution: ${executionId}`);\n    // Wait for stream connection before starting\n    console.log(`[Orchestration Starter] Waiting for stream connection...`);\n    await waitForStreamConnection(executionId, 10000); // Wait up to 10 seconds\n    // Use service role client for internal operations to bypass RLS\n    const supabase = (0,_lib_supabase_server__WEBPACK_IMPORTED_MODULE_0__.createSupabaseServiceClient)();\n    // Test basic connectivity first\n    const { data: testData, error: testError } = await supabase.from('orchestration_executions').select('id').limit(1);\n    console.log(`[Orchestration Starter] Connection test:`, {\n        testData,\n        testError\n    });\n    // Get the execution record using service role (bypasses RLS)\n    const { data: execution, error: executionError } = await supabase.from('orchestration_executions').select('*').eq('id', executionId).single();\n    console.log(`[Orchestration Starter] Query result:`, {\n        execution,\n        executionError\n    });\n    if (executionError || !execution) {\n        console.error(`[Orchestration Starter] Execution not found:`, {\n            executionId,\n            error: executionError\n        });\n        throw new Error(`Orchestration execution not found: ${executionId}`);\n    }\n    // Check if execution is in the right state\n    if (execution.status !== 'in_progress') {\n        console.log(`[Orchestration Starter] Execution ${executionId} is not in progress (status: ${execution.status}), skipping`);\n        return;\n    }\n    // Get the steps for this execution\n    const { data: steps, error: stepsError } = await supabase.from('orchestration_steps').select('*').eq('execution_id', executionId).order('step_number');\n    if (stepsError || !steps || steps.length === 0) {\n        console.error(`[Orchestration Starter] No steps found for execution ${executionId}:`, stepsError);\n        throw new Error(`No orchestration steps found for execution: ${executionId}`);\n    }\n    console.log(`[Orchestration Starter] Found ${steps.length} steps for execution ${executionId}`);\n    // Start processing the first step\n    const firstStep = steps[0];\n    if (firstStep.status === 'pending') {\n        console.log(`[Orchestration Starter] Starting first step: ${firstStep.id}`);\n        // Update step status to in_progress\n        const { error: updateError } = await supabase.from('orchestration_steps').update({\n            status: 'in_progress',\n            started_at: new Date().toISOString()\n        }).eq('id', firstStep.id);\n        if (updateError) {\n            console.error(`[Orchestration Starter] Failed to update step status:`, updateError);\n            throw new Error(`Failed to update step status: ${updateError.message}`);\n        }\n        // Process the step\n        try {\n            await processOrchestrationStep(firstStep, supabase);\n        } catch (error) {\n            console.error(`[Orchestration Starter] Error processing first step:`, error);\n            // Mark step as failed\n            await supabase.from('orchestration_steps').update({\n                status: 'failed',\n                completed_at: new Date().toISOString(),\n                error_message: error instanceof Error ? error.message : String(error)\n            }).eq('id', firstStep.id);\n            throw error;\n        }\n    }\n    console.log(`[Orchestration Starter] Successfully started orchestration for ${executionId}`);\n}\nasync function processOrchestrationStep(step, supabase) {\n    console.log(`[Orchestration Starter] Processing step ${step.step_number}: ${step.role_id}`);\n    // This would normally call the process-step endpoint or the actual processing logic\n    // For now, let's make a direct call to the process-step logic\n    const origin = process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000';\n    try {\n        const response = await fetch(`${origin}/api/orchestration/process-step`, {\n            method: 'POST',\n            headers: {\n                'Content-Type': 'application/json'\n            },\n            body: JSON.stringify({\n                stepId: step.id\n            })\n        });\n        if (!response.ok) {\n            const errorText = await response.text();\n            throw new Error(`Process step failed: ${response.status} - ${errorText}`);\n        }\n        console.log(`[Orchestration Starter] Successfully triggered processing for step ${step.id}`);\n    } catch (error) {\n        console.error(`[Orchestration Starter] Error triggering step processing:`, error);\n        throw error;\n    }\n}\nasync function waitForStreamConnection(executionId, timeoutMs) {\n    const startTime = Date.now();\n    while(Date.now() - startTime < timeoutMs){\n        const activeStreams = (0,_utils_orchestrationUtils__WEBPACK_IMPORTED_MODULE_1__.getActiveStreamCount)();\n        console.log(`[Orchestration Starter] Checking for stream connection... Active streams: ${activeStreams}`);\n        if (activeStreams > 0) {\n            console.log(`[Orchestration Starter] Stream connection detected! Proceeding with orchestration.`);\n            return;\n        }\n        // Wait 500ms before checking again\n        await new Promise((resolve)=>setTimeout(resolve, 500));\n    }\n    console.warn(`[Orchestration Starter] No stream connection detected after ${timeoutMs}ms, proceeding anyway...`);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/orchestration/orchestrationStarter.ts\n");

/***/ }),

/***/ "(rsc)/./src/utils/orchestrationUtils.ts":
/*!*****************************************!*\
  !*** ./src/utils/orchestrationUtils.ts ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   broadcastOrchestrationEvent: () => (/* binding */ broadcastOrchestrationEvent),\n/* harmony export */   calculateExecutionStrategy: () => (/* binding */ calculateExecutionStrategy),\n/* harmony export */   cleanupOldStreams: () => (/* binding */ cleanupOldStreams),\n/* harmony export */   createOrchestrationEventStream: () => (/* binding */ createOrchestrationEventStream),\n/* harmony export */   decomposeTaskWithDependencies: () => (/* binding */ decomposeTaskWithDependencies),\n/* harmony export */   emitOrchestrationEvent: () => (/* binding */ emitOrchestrationEvent),\n/* harmony export */   generateConversationalMessage: () => (/* binding */ generateConversationalMessage),\n/* harmony export */   generateModeratorCommentary: () => (/* binding */ generateModeratorCommentary),\n/* harmony export */   getActiveStreamCount: () => (/* binding */ getActiveStreamCount),\n/* harmony export */   registerStreamController: () => (/* binding */ registerStreamController),\n/* harmony export */   unregisterStreamController: () => (/* binding */ unregisterStreamController)\n/* harmony export */ });\n// Enhanced orchestration utilities for multi-role AI team collaboration\n// Create a Server-Sent Events stream for orchestration updates\nfunction createOrchestrationEventStream(executionId) {\n    const encoder = new TextEncoder();\n    return new ReadableStream({\n        start (controller) {\n            // Send initial connection event\n            const initialEvent = {\n                id: crypto.randomUUID(),\n                execution_id: executionId,\n                type: 'orchestration_started',\n                timestamp: new Date().toISOString(),\n                data: {\n                    message: 'Orchestration stream connected'\n                }\n            };\n            const eventData = `data: ${JSON.stringify(initialEvent)}\\n\\n`;\n            controller.enqueue(encoder.encode(eventData));\n        },\n        cancel () {\n            console.log(`[Orchestration Stream] Stream cancelled for execution ${executionId}`);\n        }\n    });\n}\n// Enhanced task decomposition with dependency analysis\nasync function decomposeTaskWithDependencies(originalPrompt, roles, classificationApiKey) {\n    // Create system prompt for intelligent task decomposition\n    const decompositionPrompt = `You are an expert AI orchestrator. Analyze this request and create an optimal workflow for multiple AI specialists.\n\nOriginal Request: \"${originalPrompt}\"\n\nAvailable Roles: ${roles.map((r)=>`${r.roleId} (confidence: ${r.confidence})`).join(', ')}\n\nCreate a detailed workflow with:\n1. Task breakdown into specific steps\n2. Dependencies between steps\n3. Opportunities for parallel processing\n4. Estimated complexity and duration\n5. Clear handoff instructions between models\n\nRespond in JSON format:\n{\n  \"workflow\": {\n    \"steps\": [\n      {\n        \"stepNumber\": 1,\n        \"roleId\": \"role_name\",\n        \"prompt\": \"specific task for this role\",\n        \"dependencies\": [],\n        \"canRunInParallel\": false,\n        \"priority\": 1,\n        \"estimatedDuration\": 30000,\n        \"moderatorInstructions\": \"how to validate and hand off results\"\n      }\n    ],\n    \"parallelGroups\": [[1], [2, 3], [4]],\n    \"totalEstimatedDuration\": 120000,\n    \"complexityScore\": 7\n  },\n  \"reasoning\": \"explanation of the workflow design\"\n}`;\n    try {\n        const response = await fetch(`https://generativelanguage.googleapis.com/v1beta/openai/chat/completions`, {\n            method: 'POST',\n            headers: {\n                'Content-Type': 'application/json',\n                'Authorization': `Bearer ${classificationApiKey}`\n            },\n            body: JSON.stringify({\n                model: 'gemini-2.0-flash-lite',\n                messages: [\n                    {\n                        role: 'system',\n                        content: 'You are an expert AI workflow orchestrator.'\n                    },\n                    {\n                        role: 'user',\n                        content: decompositionPrompt\n                    }\n                ],\n                temperature: 0.3,\n                max_tokens: 1500,\n                response_format: {\n                    type: \"json_object\"\n                }\n            })\n        });\n        if (!response.ok) {\n            throw new Error(`Decomposition API error: ${response.status}`);\n        }\n        const result = await response.json();\n        const content = result.choices?.[0]?.message?.content;\n        if (!content) {\n            throw new Error('Empty decomposition response');\n        }\n        const parsed = JSON.parse(content);\n        return parsed.workflow;\n    } catch (error) {\n        console.warn(`[Task Decomposition] Error: ${error}, falling back to simple decomposition`);\n        // Fallback to simple sequential decomposition\n        return createFallbackWorkflow(originalPrompt, roles);\n    }\n}\n// Fallback workflow creation for when AI decomposition fails\nfunction createFallbackWorkflow(originalPrompt, roles) {\n    const sortedRoles = [\n        ...roles\n    ].sort((a, b)=>a.executionOrder - b.executionOrder);\n    const steps = sortedRoles.map((role, index)=>({\n            stepNumber: index + 1,\n            roleId: role.roleId,\n            prompt: index === 0 ? `Handle the ${role.roleId} aspect of this request: \"${originalPrompt}\"` : `Continue with the ${role.roleId} aspect based on the previous step's output: {{previousOutput}}`,\n            dependencies: index === 0 ? [] : [\n                index\n            ],\n            canRunInParallel: false,\n            priority: index + 1,\n            estimatedDuration: 45000,\n            moderatorInstructions: `Validate the ${role.roleId} output and prepare for next step`\n        }));\n    return {\n        steps,\n        parallelGroups: steps.map((_, index)=>[\n                index + 1\n            ]),\n        totalEstimatedDuration: steps.length * 45000,\n        complexityScore: Math.min(steps.length * 2, 10)\n    };\n}\n// Global map to track active streams (moved from route file)\nconst activeStreams = new Map();\n// Broadcast event to all connected clients for an execution\nfunction broadcastOrchestrationEvent(executionId, event) {\n    const streamInfo = activeStreams.get(executionId);\n    if (streamInfo) {\n        try {\n            const encoder = new TextEncoder();\n            const eventData = `id: ${event.id}\\nevent: ${event.type}\\ndata: ${JSON.stringify(event)}\\n\\n`;\n            // Check if controller is still writable\n            if (streamInfo.controller.desiredSize !== null) {\n                streamInfo.controller.enqueue(encoder.encode(eventData));\n                streamInfo.lastEventId = event.id;\n                console.log(`[Orchestration Stream] Broadcasted ${event.type} to execution ${executionId}`);\n            } else {\n                console.warn(`[Orchestration Stream] Controller not writable for execution ${executionId}, removing stream`);\n                activeStreams.delete(executionId);\n            }\n        } catch (error) {\n            console.error(`[Orchestration Stream] Error broadcasting event: ${error}`);\n            // Remove dead stream\n            activeStreams.delete(executionId);\n            // If it's a connection error, log it specifically\n            if (error instanceof TypeError && error.message.includes('enqueue')) {\n                console.warn(`[Orchestration Stream] Stream closed for execution ${executionId}, client likely disconnected`);\n            }\n        }\n    } else {\n        console.warn(`[Orchestration Stream] No active stream found for execution ${executionId}`);\n    }\n}\n// Register a stream controller\nfunction registerStreamController(executionId, controller) {\n    activeStreams.set(executionId, {\n        controller,\n        lastEventId: '',\n        startTime: Date.now()\n    });\n}\n// Unregister a stream controller\nfunction unregisterStreamController(executionId) {\n    activeStreams.delete(executionId);\n}\n// Get active stream count for monitoring\nfunction getActiveStreamCount() {\n    return activeStreams.size;\n}\n// Clean up old streams (called periodically)\nfunction cleanupOldStreams(maxAgeMs = 30 * 60 * 1000) {\n    const now = Date.now();\n    for (const [executionId, streamInfo] of activeStreams.entries()){\n        if (now - streamInfo.startTime > maxAgeMs) {\n            try {\n                streamInfo.controller.close();\n            } catch (error) {\n                console.warn(`[Orchestration Stream] Error closing old stream: ${error}`);\n            }\n            activeStreams.delete(executionId);\n            console.log(`[Orchestration Stream] Cleaned up old stream for execution ${executionId}`);\n        }\n    }\n}\n// Emit orchestration event to stream\nasync function emitOrchestrationEvent(executionId, eventType, data, stepNumber, roleId, modelName) {\n    const event = {\n        id: crypto.randomUUID(),\n        execution_id: executionId,\n        type: eventType,\n        timestamp: new Date().toISOString(),\n        data,\n        step_number: stepNumber,\n        role_id: roleId,\n        model_name: modelName\n    };\n    // Broadcast to active streams\n    broadcastOrchestrationEvent(executionId, event);\n    // Store event in database for persistence and replay\n    try {\n        console.log(`[Orchestration Event] ${eventType}:`, event);\n    // In a real implementation, you'd store this in the orchestration_events table\n    // and broadcast to connected clients via WebSocket or SSE\n    } catch (error) {\n        console.error(`[Orchestration Event] Failed to emit ${eventType}:`, error);\n    }\n}\n// Generate moderator commentary for entertainment value\nfunction generateModeratorCommentary(eventType, stepData, roleId) {\n    const commentaries = {\n        orchestration_started: [\n            \"🎬 Alright team, we've got an interesting challenge ahead!\",\n            \"🚀 Let's break this down and see who's best suited for each part.\",\n            \"🎯 Time to coordinate our AI specialists for optimal results.\"\n        ],\n        task_decomposed: [\n            \"📋 Task analysis complete! I've identified the perfect team composition.\",\n            \"🎪 Perfect breakdown! Each specialist will handle their area of expertise.\",\n            \"⚡ Task decomposition successful! Ready to assign specialists.\"\n        ],\n        step_assigned: [\n            `📋 Assigning the ${roleId} specialist to handle this part.`,\n            `🎪 Our ${roleId} expert is stepping up to the plate!`,\n            `⚡ Perfect match - ${roleId} is exactly what we need here.`\n        ],\n        step_started: [\n            `🔥 ${roleId} is now working their magic...`,\n            `⚙️ Watch ${roleId} tackle this challenge in real-time!`,\n            `🎨 ${roleId} is crafting something special for us.`\n        ],\n        step_progress: [\n            `⚡ ${roleId} is making excellent progress...`,\n            `🎯 ${roleId} is deep in the zone, crafting quality work.`,\n            `🔥 ${roleId} is on fire! Great momentum building.`\n        ],\n        step_streaming: [\n            `📡 ${roleId} is streaming their work live...`,\n            `⚡ Real-time updates coming from ${roleId}!`,\n            `🎬 ${roleId} is broadcasting their creative process.`\n        ],\n        step_completed: [\n            `✅ Excellent work from ${roleId}! Moving to the next phase.`,\n            `🎉 ${roleId} delivered exactly what we needed. Handoff time!`,\n            `💫 Beautiful execution by ${roleId}. The team is flowing perfectly.`\n        ],\n        step_failed: [\n            `⚠️ ${roleId} hit a snag, but we're adapting quickly.`,\n            `🔄 ${roleId} needs a different approach. Recalibrating...`,\n            `🛠️ ${roleId} encountered an issue. Team is problem-solving.`\n        ],\n        synthesis_started: [\n            \"🧩 Now I'm weaving all these pieces together...\",\n            \"🎭 Time for the grand finale - combining all our specialists' work!\",\n            \"🌟 Watch as I synthesize these brilliant contributions into one cohesive result.\"\n        ],\n        synthesis_progress: [\n            \"🎨 Synthesis is flowing beautifully...\",\n            \"⚡ Combining all the brilliant work into something amazing.\",\n            \"🧩 The pieces are coming together perfectly.\"\n        ],\n        synthesis_streaming: [\n            \"📡 Streaming the final synthesis live...\",\n            \"⚡ Real-time synthesis in progress!\",\n            \"🎬 Watch the magic happen as everything combines.\"\n        ],\n        synthesis_complete: [\n            \"🎊 Mission accomplished! The team has delivered an outstanding result.\",\n            \"🏆 Incredible teamwork! We've created something truly exceptional together.\",\n            \"🌟 Perfect execution! This is what happens when AI specialists collaborate brilliantly.\"\n        ],\n        orchestration_completed: [\n            \"🎉 Full orchestration complete! What an amazing team effort.\",\n            \"🏆 Mission accomplished! The AI team delivered excellence.\",\n            \"✨ Orchestration finished successfully. Outstanding collaboration!\"\n        ],\n        orchestration_failed: [\n            \"⚠️ Orchestration encountered issues. Analyzing for recovery.\",\n            \"🔄 Team hit some challenges. Regrouping for next attempt.\",\n            \"🛠️ Orchestration needs adjustment. Learning from this experience.\"\n        ],\n        moderator_commentary: [\n            \"🎙️ Moderator providing live insights...\",\n            \"📢 Commentary update from the coordination team.\",\n            \"🎯 Strategic guidance from the moderator.\"\n        ],\n        specialist_message: [\n            `💬 ${roleId} has an important update to share.`,\n            `📝 Message from ${roleId} specialist.`,\n            `🗣️ ${roleId} is communicating with the team.`\n        ],\n        moderator_assignment: [\n            `🎯 @${roleId}, you're up! Please begin your specialized work.`,\n            `📋 @${roleId}, this is right in your wheelhouse. Take it away!`,\n            `⚡ @${roleId}, I need your expertise here. Please work your magic.`\n        ],\n        specialist_acknowledgment: [\n            `✅ ${roleId} acknowledges the assignment and is ready to work.`,\n            `🎯 ${roleId} confirms understanding and begins task execution.`,\n            `⚡ ${roleId} is locked and loaded, ready to deliver excellence.`\n        ],\n        handoff_message: [\n            `✨ Excellent work, @${roleId}! Quality looks great. Passing to next specialist...`,\n            `👏 Outstanding execution, @${roleId}! Moving to the next phase...`,\n            `🎉 Fantastic results, @${roleId}! The team collaboration continues...`\n        ],\n        clarification_request: [\n            `❓ ${roleId} needs clarification to deliver the best results.`,\n            `🤔 ${roleId} is asking for additional context to optimize their work.`,\n            `💭 ${roleId} wants to ensure they understand the requirements perfectly.`\n        ],\n        clarification_response: [\n            `💡 Clarification provided! ${roleId} can now proceed with confidence.`,\n            `✅ Question answered! ${roleId} has the context they need.`,\n            `🎯 Perfect! ${roleId} now has all the details for optimal execution.`\n        ]\n    };\n    const options = commentaries[eventType] || [\n        \"🤖 Processing...\"\n    ];\n    return options[Math.floor(Math.random() * options.length)];\n}\n// Generate conversational messages for chatroom experience\nfunction generateConversationalMessage(eventType, roleId, context) {\n    const messages = {\n        orchestration_started: [\n            \"🎬 Welcome to the AI Team Collaboration! I'm assembling the perfect team for this task.\",\n            \"🚀 Alright everyone, we've got an exciting challenge ahead. Let me coordinate our specialists.\",\n            \"🎯 Time to bring together our expert AI team for optimal results!\"\n        ],\n        task_decomposed: [\n            \"📋 I've analyzed the task and assembled this expert team. Let's begin the collaboration!\",\n            \"🎪 Perfect! I've matched the right specialists to each part of this challenge.\",\n            \"⚡ Task breakdown complete. Our team is perfectly positioned for success.\"\n        ],\n        moderator_assignment: [\n            `🎯 @${roleId}, you're up! Please begin your specialized work on this task.`,\n            `📋 @${roleId}, this is right in your wheelhouse. Take it away!`,\n            `⚡ @${roleId}, I need your expertise here. Please work your magic.`\n        ],\n        specialist_acknowledgment: [\n            `✅ Understood! I'm ${roleId} and I'll handle this task with expertise. Starting work now...`,\n            `🎯 Perfect! As the ${roleId} specialist, I'm excited to tackle this challenge.`,\n            `⚡ Got it! ${roleId} here - I'll deliver excellent results for the team.`\n        ],\n        specialist_message: [\n            `🎉 Excellent! I've completed my part of the task. Here's what I've delivered:`,\n            `✨ Perfect! My specialized work is complete. Take a look at the results:`,\n            `🚀 Mission accomplished! Here's my contribution to the team effort:`\n        ],\n        handoff_message: [\n            `✨ Excellent work, @${roleId}! Quality looks great. Now passing to the next specialist...`,\n            `👏 Outstanding execution, @${roleId}! Moving to the next phase...`,\n            `🎉 Fantastic results, @${roleId}! The team collaboration continues...`\n        ],\n        synthesis_started: [\n            \"🧩 Fantastic teamwork everyone! Now I'll synthesize all your excellent contributions into the final deliverable...\",\n            \"🎨 Amazing collaboration! Time to weave all these brilliant outputs together...\",\n            \"✨ Outstanding work team! Let me combine everything into the perfect final result...\"\n        ],\n        synthesis_complete: [\n            \"🎊 Mission accomplished! The team has delivered an outstanding result. Great collaboration everyone!\",\n            \"🏆 Incredible teamwork! We've created something truly exceptional together.\",\n            \"🌟 Perfect execution! This is what happens when AI specialists collaborate brilliantly.\"\n        ]\n    };\n    const options = messages[eventType] || [\n        \"🤖 Processing...\"\n    ];\n    const selectedMessage = options[Math.floor(Math.random() * options.length)];\n    // Replace placeholders with context if available\n    if (context && typeof selectedMessage === 'string') {\n        return selectedMessage.replace(/\\{(\\w+)\\}/g, (match, key)=>context[key] || match);\n    }\n    return selectedMessage;\n}\n// Calculate optimal execution strategy (parallel vs sequential)\nfunction calculateExecutionStrategy(workflow) {\n    const totalSteps = workflow.steps.length;\n    const parallelGroups = workflow.parallelGroups.length;\n    if (parallelGroups === totalSteps) {\n        return {\n            strategy: 'sequential',\n            estimatedSpeedup: 1.0,\n            riskLevel: 'low'\n        };\n    }\n    if (parallelGroups === 1) {\n        return {\n            strategy: 'parallel',\n            estimatedSpeedup: totalSteps * 0.7,\n            riskLevel: 'high'\n        };\n    }\n    return {\n        strategy: 'hybrid',\n        estimatedSpeedup: totalSteps / parallelGroups * 0.8,\n        riskLevel: 'medium'\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvdXRpbHMvb3JjaGVzdHJhdGlvblV0aWxzLnRzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7O0FBQUEsd0VBQXdFO0FBOER4RSwrREFBK0Q7QUFDeEQsU0FBU0EsK0JBQStCQyxXQUFtQjtJQUNoRSxNQUFNQyxVQUFVLElBQUlDO0lBRXBCLE9BQU8sSUFBSUMsZUFBZTtRQUN4QkMsT0FBTUMsVUFBVTtZQUNkLGdDQUFnQztZQUNoQyxNQUFNQyxlQUFtQztnQkFDdkNDLElBQUlDLE9BQU9DLFVBQVU7Z0JBQ3JCQyxjQUFjVjtnQkFDZFcsTUFBTTtnQkFDTkMsV0FBVyxJQUFJQyxPQUFPQyxXQUFXO2dCQUNqQ0MsTUFBTTtvQkFBRUMsU0FBUztnQkFBaUM7WUFDcEQ7WUFFQSxNQUFNQyxZQUFZLENBQUMsTUFBTSxFQUFFQyxLQUFLQyxTQUFTLENBQUNiLGNBQWMsSUFBSSxDQUFDO1lBQzdERCxXQUFXZSxPQUFPLENBQUNuQixRQUFRb0IsTUFBTSxDQUFDSjtRQUNwQztRQUVBSztZQUNFQyxRQUFRQyxHQUFHLENBQUMsQ0FBQyxzREFBc0QsRUFBRXhCLGFBQWE7UUFDcEY7SUFDRjtBQUNGO0FBRUEsdURBQXVEO0FBQ2hELGVBQWV5Qiw4QkFDcEJDLGNBQXNCLEVBQ3RCQyxLQUE0RSxFQUM1RUMsb0JBQTRCO0lBRTVCLDBEQUEwRDtJQUMxRCxNQUFNQyxzQkFBc0IsQ0FBQzs7bUJBRVosRUFBRUgsZUFBZTs7aUJBRW5CLEVBQUVDLE1BQU1HLEdBQUcsQ0FBQ0MsQ0FBQUEsSUFBSyxHQUFHQSxFQUFFQyxNQUFNLENBQUMsY0FBYyxFQUFFRCxFQUFFRSxVQUFVLENBQUMsQ0FBQyxDQUFDLEVBQUVDLElBQUksQ0FBQyxNQUFNOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztDQTZCekYsQ0FBQztJQUVBLElBQUk7UUFDRixNQUFNQyxXQUFXLE1BQU1DLE1BQU0sQ0FBQyx3RUFBd0UsQ0FBQyxFQUFFO1lBQ3ZHQyxRQUFRO1lBQ1JDLFNBQVM7Z0JBQ1AsZ0JBQWdCO2dCQUNoQixpQkFBaUIsQ0FBQyxPQUFPLEVBQUVWLHNCQUFzQjtZQUNuRDtZQUNBVyxNQUFNckIsS0FBS0MsU0FBUyxDQUFDO2dCQUNuQnFCLE9BQU87Z0JBQ1BDLFVBQVU7b0JBQ1I7d0JBQUVDLE1BQU07d0JBQVVDLFNBQVM7b0JBQThDO29CQUN6RTt3QkFBRUQsTUFBTTt3QkFBUUMsU0FBU2Q7b0JBQW9CO2lCQUM5QztnQkFDRGUsYUFBYTtnQkFDYkMsWUFBWTtnQkFDWkMsaUJBQWlCO29CQUFFbkMsTUFBTTtnQkFBYztZQUN6QztRQUNGO1FBRUEsSUFBSSxDQUFDd0IsU0FBU1ksRUFBRSxFQUFFO1lBQ2hCLE1BQU0sSUFBSUMsTUFBTSxDQUFDLHlCQUF5QixFQUFFYixTQUFTYyxNQUFNLEVBQUU7UUFDL0Q7UUFFQSxNQUFNQyxTQUFTLE1BQU1mLFNBQVNnQixJQUFJO1FBQ2xDLE1BQU1SLFVBQVVPLE9BQU9FLE9BQU8sRUFBRSxDQUFDLEVBQUUsRUFBRXBDLFNBQVMyQjtRQUU5QyxJQUFJLENBQUNBLFNBQVM7WUFDWixNQUFNLElBQUlLLE1BQU07UUFDbEI7UUFFQSxNQUFNSyxTQUFTbkMsS0FBS29DLEtBQUssQ0FBQ1g7UUFDMUIsT0FBT1UsT0FBT0UsUUFBUTtJQUV4QixFQUFFLE9BQU9DLE9BQU87UUFDZGpDLFFBQVFrQyxJQUFJLENBQUMsQ0FBQyw0QkFBNEIsRUFBRUQsTUFBTSxzQ0FBc0MsQ0FBQztRQUV6Riw4Q0FBOEM7UUFDOUMsT0FBT0UsdUJBQXVCaEMsZ0JBQWdCQztJQUNoRDtBQUNGO0FBRUEsNkRBQTZEO0FBQzdELFNBQVMrQix1QkFDUGhDLGNBQXNCLEVBQ3RCQyxLQUE0RTtJQUU1RSxNQUFNZ0MsY0FBYztXQUFJaEM7S0FBTSxDQUFDaUMsSUFBSSxDQUFDLENBQUNDLEdBQUdDLElBQU1ELEVBQUVFLGNBQWMsR0FBR0QsRUFBRUMsY0FBYztJQUVqRixNQUFNQyxRQUFxQ0wsWUFBWTdCLEdBQUcsQ0FBQyxDQUFDWSxNQUFNdUIsUUFBVztZQUMzRUMsWUFBWUQsUUFBUTtZQUNwQmpDLFFBQVFVLEtBQUtWLE1BQU07WUFDbkJtQyxRQUFRRixVQUFVLElBQ2QsQ0FBQyxXQUFXLEVBQUV2QixLQUFLVixNQUFNLENBQUMsMEJBQTBCLEVBQUVOLGVBQWUsQ0FBQyxDQUFDLEdBQ3ZFLENBQUMsa0JBQWtCLEVBQUVnQixLQUFLVixNQUFNLENBQUMsK0RBQStELENBQUM7WUFDckdvQyxjQUFjSCxVQUFVLElBQUksRUFBRSxHQUFHO2dCQUFDQTthQUFNO1lBQ3hDSSxrQkFBa0I7WUFDbEJDLFVBQVVMLFFBQVE7WUFDbEJNLG1CQUFtQjtZQUNuQkMsdUJBQXVCLENBQUMsYUFBYSxFQUFFOUIsS0FBS1YsTUFBTSxDQUFDLGlDQUFpQyxDQUFDO1FBQ3ZGO0lBRUEsT0FBTztRQUNMZ0M7UUFDQVMsZ0JBQWdCVCxNQUFNbEMsR0FBRyxDQUFDLENBQUM0QyxHQUFHVCxRQUFVO2dCQUFDQSxRQUFRO2FBQUU7UUFDbkRVLHdCQUF3QlgsTUFBTVksTUFBTSxHQUFHO1FBQ3ZDQyxpQkFBaUJDLEtBQUtDLEdBQUcsQ0FBQ2YsTUFBTVksTUFBTSxHQUFHLEdBQUc7SUFDOUM7QUFDRjtBQUVBLDZEQUE2RDtBQUM3RCxNQUFNSSxnQkFBZ0IsSUFBSUM7QUFNMUIsNERBQTREO0FBQ3JELFNBQVNDLDRCQUNkbEYsV0FBbUIsRUFDbkJtRixLQUF5QjtJQUV6QixNQUFNQyxhQUFhSixjQUFjSyxHQUFHLENBQUNyRjtJQUVyQyxJQUFJb0YsWUFBWTtRQUNkLElBQUk7WUFDRixNQUFNbkYsVUFBVSxJQUFJQztZQUNwQixNQUFNZSxZQUFZLENBQUMsSUFBSSxFQUFFa0UsTUFBTTVFLEVBQUUsQ0FBQyxTQUFTLEVBQUU0RSxNQUFNeEUsSUFBSSxDQUFDLFFBQVEsRUFBRU8sS0FBS0MsU0FBUyxDQUFDZ0UsT0FBTyxJQUFJLENBQUM7WUFFN0Ysd0NBQXdDO1lBQ3hDLElBQUlDLFdBQVcvRSxVQUFVLENBQUNpRixXQUFXLEtBQUssTUFBTTtnQkFDOUNGLFdBQVcvRSxVQUFVLENBQUNlLE9BQU8sQ0FBQ25CLFFBQVFvQixNQUFNLENBQUNKO2dCQUM3Q21FLFdBQVdHLFdBQVcsR0FBR0osTUFBTTVFLEVBQUU7Z0JBQ2pDZ0IsUUFBUUMsR0FBRyxDQUFDLENBQUMsbUNBQW1DLEVBQUUyRCxNQUFNeEUsSUFBSSxDQUFDLGNBQWMsRUFBRVgsYUFBYTtZQUM1RixPQUFPO2dCQUNMdUIsUUFBUWtDLElBQUksQ0FBQyxDQUFDLDZEQUE2RCxFQUFFekQsWUFBWSxpQkFBaUIsQ0FBQztnQkFDM0dnRixjQUFjUSxNQUFNLENBQUN4RjtZQUN2QjtRQUNGLEVBQUUsT0FBT3dELE9BQU87WUFDZGpDLFFBQVFpQyxLQUFLLENBQUMsQ0FBQyxpREFBaUQsRUFBRUEsT0FBTztZQUN6RSxxQkFBcUI7WUFDckJ3QixjQUFjUSxNQUFNLENBQUN4RjtZQUVyQixrREFBa0Q7WUFDbEQsSUFBSXdELGlCQUFpQmlDLGFBQWFqQyxNQUFNeEMsT0FBTyxDQUFDMEUsUUFBUSxDQUFDLFlBQVk7Z0JBQ25FbkUsUUFBUWtDLElBQUksQ0FBQyxDQUFDLG1EQUFtRCxFQUFFekQsWUFBWSw0QkFBNEIsQ0FBQztZQUM5RztRQUNGO0lBQ0YsT0FBTztRQUNMdUIsUUFBUWtDLElBQUksQ0FBQyxDQUFDLDREQUE0RCxFQUFFekQsYUFBYTtJQUMzRjtBQUNGO0FBRUEsK0JBQStCO0FBQ3hCLFNBQVMyRix5QkFDZDNGLFdBQW1CLEVBQ25CSyxVQUEyQztJQUUzQzJFLGNBQWNZLEdBQUcsQ0FBQzVGLGFBQWE7UUFDN0JLO1FBQ0FrRixhQUFhO1FBQ2JNLFdBQVdoRixLQUFLaUYsR0FBRztJQUNyQjtBQUNGO0FBRUEsaUNBQWlDO0FBQzFCLFNBQVNDLDJCQUEyQi9GLFdBQW1CO0lBQzVEZ0YsY0FBY1EsTUFBTSxDQUFDeEY7QUFDdkI7QUFFQSx5Q0FBeUM7QUFDbEMsU0FBU2dHO0lBQ2QsT0FBT2hCLGNBQWNpQixJQUFJO0FBQzNCO0FBRUEsNkNBQTZDO0FBQ3RDLFNBQVNDLGtCQUFrQkMsV0FBbUIsS0FBSyxLQUFLLElBQUk7SUFDakUsTUFBTUwsTUFBTWpGLEtBQUtpRixHQUFHO0lBRXBCLEtBQUssTUFBTSxDQUFDOUYsYUFBYW9GLFdBQVcsSUFBSUosY0FBY29CLE9BQU8sR0FBSTtRQUMvRCxJQUFJTixNQUFNVixXQUFXUyxTQUFTLEdBQUdNLFVBQVU7WUFDekMsSUFBSTtnQkFDRmYsV0FBVy9FLFVBQVUsQ0FBQ2dHLEtBQUs7WUFDN0IsRUFBRSxPQUFPN0MsT0FBTztnQkFDZGpDLFFBQVFrQyxJQUFJLENBQUMsQ0FBQyxpREFBaUQsRUFBRUQsT0FBTztZQUMxRTtZQUNBd0IsY0FBY1EsTUFBTSxDQUFDeEY7WUFDckJ1QixRQUFRQyxHQUFHLENBQUMsQ0FBQywyREFBMkQsRUFBRXhCLGFBQWE7UUFDekY7SUFDRjtBQUNGO0FBRUEscUNBQXFDO0FBQzlCLGVBQWVzRyx1QkFDcEJ0RyxXQUFtQixFQUNuQnVHLFNBQWlDLEVBQ2pDeEYsSUFBUyxFQUNUbUQsVUFBbUIsRUFDbkJsQyxNQUFlLEVBQ2Z3RSxTQUFrQjtJQUVsQixNQUFNckIsUUFBNEI7UUFDaEM1RSxJQUFJQyxPQUFPQyxVQUFVO1FBQ3JCQyxjQUFjVjtRQUNkVyxNQUFNNEY7UUFDTjNGLFdBQVcsSUFBSUMsT0FBT0MsV0FBVztRQUNqQ0M7UUFDQTBGLGFBQWF2QztRQUNid0MsU0FBUzFFO1FBQ1QyRSxZQUFZSDtJQUNkO0lBRUEsOEJBQThCO0lBQzlCdEIsNEJBQTRCbEYsYUFBYW1GO0lBRXpDLHFEQUFxRDtJQUNyRCxJQUFJO1FBQ0Y1RCxRQUFRQyxHQUFHLENBQUMsQ0FBQyxzQkFBc0IsRUFBRStFLFVBQVUsQ0FBQyxDQUFDLEVBQUVwQjtJQUVuRCwrRUFBK0U7SUFDL0UsMERBQTBEO0lBRTVELEVBQUUsT0FBTzNCLE9BQU87UUFDZGpDLFFBQVFpQyxLQUFLLENBQUMsQ0FBQyxxQ0FBcUMsRUFBRStDLFVBQVUsQ0FBQyxDQUFDLEVBQUUvQztJQUN0RTtBQUNGO0FBRUEsd0RBQXdEO0FBQ2pELFNBQVNvRCw0QkFDZEwsU0FBaUMsRUFDakNNLFFBQWEsRUFDYjdFLE1BQWU7SUFFZixNQUFNOEUsZUFBeUQ7UUFDN0RDLHVCQUF1QjtZQUNyQjtZQUNBO1lBQ0E7U0FDRDtRQUNEQyxpQkFBaUI7WUFDZjtZQUNBO1lBQ0E7U0FDRDtRQUNEQyxlQUFlO1lBQ2IsQ0FBQyxpQkFBaUIsRUFBRWpGLE9BQU8sZ0NBQWdDLENBQUM7WUFDNUQsQ0FBQyxPQUFPLEVBQUVBLE9BQU8sb0NBQW9DLENBQUM7WUFDdEQsQ0FBQyxrQkFBa0IsRUFBRUEsT0FBTyw4QkFBOEIsQ0FBQztTQUM1RDtRQUNEa0YsY0FBYztZQUNaLENBQUMsR0FBRyxFQUFFbEYsT0FBTyw4QkFBOEIsQ0FBQztZQUM1QyxDQUFDLFNBQVMsRUFBRUEsT0FBTyxvQ0FBb0MsQ0FBQztZQUN4RCxDQUFDLEdBQUcsRUFBRUEsT0FBTyxzQ0FBc0MsQ0FBQztTQUNyRDtRQUNEbUYsZUFBZTtZQUNiLENBQUMsRUFBRSxFQUFFbkYsT0FBTyxnQ0FBZ0MsQ0FBQztZQUM3QyxDQUFDLEdBQUcsRUFBRUEsT0FBTyw0Q0FBNEMsQ0FBQztZQUMxRCxDQUFDLEdBQUcsRUFBRUEsT0FBTyxxQ0FBcUMsQ0FBQztTQUNwRDtRQUNEb0YsZ0JBQWdCO1lBQ2QsQ0FBQyxHQUFHLEVBQUVwRixPQUFPLGdDQUFnQyxDQUFDO1lBQzlDLENBQUMsZ0NBQWdDLEVBQUVBLE9BQU8sQ0FBQyxDQUFDO1lBQzVDLENBQUMsR0FBRyxFQUFFQSxPQUFPLHdDQUF3QyxDQUFDO1NBQ3ZEO1FBQ0RxRixnQkFBZ0I7WUFDZCxDQUFDLHNCQUFzQixFQUFFckYsT0FBTywyQkFBMkIsQ0FBQztZQUM1RCxDQUFDLEdBQUcsRUFBRUEsT0FBTyxnREFBZ0QsQ0FBQztZQUM5RCxDQUFDLDBCQUEwQixFQUFFQSxPQUFPLGdDQUFnQyxDQUFDO1NBQ3RFO1FBQ0RzRixhQUFhO1lBQ1gsQ0FBQyxHQUFHLEVBQUV0RixPQUFPLHdDQUF3QyxDQUFDO1lBQ3RELENBQUMsR0FBRyxFQUFFQSxPQUFPLDZDQUE2QyxDQUFDO1lBQzNELENBQUMsSUFBSSxFQUFFQSxPQUFPLCtDQUErQyxDQUFDO1NBQy9EO1FBQ0R1RixtQkFBbUI7WUFDakI7WUFDQTtZQUNBO1NBQ0Q7UUFDREMsb0JBQW9CO1lBQ2xCO1lBQ0E7WUFDQTtTQUNEO1FBQ0RDLHFCQUFxQjtZQUNuQjtZQUNBO1lBQ0E7U0FDRDtRQUNEQyxvQkFBb0I7WUFDbEI7WUFDQTtZQUNBO1NBQ0Q7UUFDREMseUJBQXlCO1lBQ3ZCO1lBQ0E7WUFDQTtTQUNEO1FBQ0RDLHNCQUFzQjtZQUNwQjtZQUNBO1lBQ0E7U0FDRDtRQUNEQyxzQkFBc0I7WUFDcEI7WUFDQTtZQUNBO1NBQ0Q7UUFDREMsb0JBQW9CO1lBQ2xCLENBQUMsR0FBRyxFQUFFOUYsT0FBTyxrQ0FBa0MsQ0FBQztZQUNoRCxDQUFDLGdCQUFnQixFQUFFQSxPQUFPLFlBQVksQ0FBQztZQUN2QyxDQUFDLElBQUksRUFBRUEsT0FBTyxnQ0FBZ0MsQ0FBQztTQUNoRDtRQUNEK0Ysc0JBQXNCO1lBQ3BCLENBQUMsSUFBSSxFQUFFL0YsT0FBTyxnREFBZ0QsQ0FBQztZQUMvRCxDQUFDLElBQUksRUFBRUEsT0FBTyxpREFBaUQsQ0FBQztZQUNoRSxDQUFDLEdBQUcsRUFBRUEsT0FBTyxxREFBcUQsQ0FBQztTQUNwRTtRQUNEZ0csMkJBQTJCO1lBQ3pCLENBQUMsRUFBRSxFQUFFaEcsT0FBTyxrREFBa0QsQ0FBQztZQUMvRCxDQUFDLEdBQUcsRUFBRUEsT0FBTyxrREFBa0QsQ0FBQztZQUNoRSxDQUFDLEVBQUUsRUFBRUEsT0FBTyxtREFBbUQsQ0FBQztTQUNqRTtRQUNEaUcsaUJBQWlCO1lBQ2YsQ0FBQyxtQkFBbUIsRUFBRWpHLE9BQU8sb0RBQW9ELENBQUM7WUFDbEYsQ0FBQywyQkFBMkIsRUFBRUEsT0FBTyw2QkFBNkIsQ0FBQztZQUNuRSxDQUFDLHVCQUF1QixFQUFFQSxPQUFPLHFDQUFxQyxDQUFDO1NBQ3hFO1FBQ0RrRyx1QkFBdUI7WUFDckIsQ0FBQyxFQUFFLEVBQUVsRyxPQUFPLGlEQUFpRCxDQUFDO1lBQzlELENBQUMsR0FBRyxFQUFFQSxPQUFPLHlEQUF5RCxDQUFDO1lBQ3ZFLENBQUMsR0FBRyxFQUFFQSxPQUFPLDREQUE0RCxDQUFDO1NBQzNFO1FBQ0RtRyx3QkFBd0I7WUFDdEIsQ0FBQywyQkFBMkIsRUFBRW5HLE9BQU8saUNBQWlDLENBQUM7WUFDdkUsQ0FBQyxxQkFBcUIsRUFBRUEsT0FBTywyQkFBMkIsQ0FBQztZQUMzRCxDQUFDLFlBQVksRUFBRUEsT0FBTywrQ0FBK0MsQ0FBQztTQUN2RTtJQUNIO0lBRUEsTUFBTW9HLFVBQVV0QixZQUFZLENBQUNQLFVBQVUsSUFBSTtRQUFDO0tBQW1CO0lBQy9ELE9BQU82QixPQUFPLENBQUN0RCxLQUFLdUQsS0FBSyxDQUFDdkQsS0FBS3dELE1BQU0sS0FBS0YsUUFBUXhELE1BQU0sRUFBRTtBQUM1RDtBQUVBLDJEQUEyRDtBQUNwRCxTQUFTMkQsOEJBQ2RoQyxTQUFpQyxFQUNqQ3ZFLE1BQWUsRUFDZndHLE9BQWE7SUFFYixNQUFNL0YsV0FBcUM7UUFDekNzRSx1QkFBdUI7WUFDckI7WUFDQTtZQUNBO1NBQ0Q7UUFDREMsaUJBQWlCO1lBQ2Y7WUFDQTtZQUNBO1NBQ0Q7UUFDRGUsc0JBQXNCO1lBQ3BCLENBQUMsSUFBSSxFQUFFL0YsT0FBTyw2REFBNkQsQ0FBQztZQUM1RSxDQUFDLElBQUksRUFBRUEsT0FBTyxpREFBaUQsQ0FBQztZQUNoRSxDQUFDLEdBQUcsRUFBRUEsT0FBTyxxREFBcUQsQ0FBQztTQUNwRTtRQUNEZ0csMkJBQTJCO1lBQ3pCLENBQUMsa0JBQWtCLEVBQUVoRyxPQUFPLCtEQUErRCxDQUFDO1lBQzVGLENBQUMsbUJBQW1CLEVBQUVBLE9BQU8sa0RBQWtELENBQUM7WUFDaEYsQ0FBQyxVQUFVLEVBQUVBLE9BQU8sb0RBQW9ELENBQUM7U0FDMUU7UUFDRDhGLG9CQUFvQjtZQUNsQixDQUFDLDZFQUE2RSxDQUFDO1lBQy9FLENBQUMsdUVBQXVFLENBQUM7WUFDekUsQ0FBQyxtRUFBbUUsQ0FBQztTQUN0RTtRQUNERyxpQkFBaUI7WUFDZixDQUFDLG1CQUFtQixFQUFFakcsT0FBTyw0REFBNEQsQ0FBQztZQUMxRixDQUFDLDJCQUEyQixFQUFFQSxPQUFPLDZCQUE2QixDQUFDO1lBQ25FLENBQUMsdUJBQXVCLEVBQUVBLE9BQU8scUNBQXFDLENBQUM7U0FDeEU7UUFDRHVGLG1CQUFtQjtZQUNqQjtZQUNBO1lBQ0E7U0FDRDtRQUNERyxvQkFBb0I7WUFDbEI7WUFDQTtZQUNBO1NBQ0Q7SUFDSDtJQUVBLE1BQU1VLFVBQVUzRixRQUFRLENBQUM4RCxVQUFVLElBQUk7UUFBQztLQUFtQjtJQUMzRCxNQUFNa0Msa0JBQWtCTCxPQUFPLENBQUN0RCxLQUFLdUQsS0FBSyxDQUFDdkQsS0FBS3dELE1BQU0sS0FBS0YsUUFBUXhELE1BQU0sRUFBRTtJQUUzRSxpREFBaUQ7SUFDakQsSUFBSTRELFdBQVcsT0FBT0Msb0JBQW9CLFVBQVU7UUFDbEQsT0FBT0EsZ0JBQWdCQyxPQUFPLENBQUMsY0FBYyxDQUFDQyxPQUFPQyxNQUFRSixPQUFPLENBQUNJLElBQUksSUFBSUQ7SUFDL0U7SUFFQSxPQUFPRjtBQUNUO0FBRUEsZ0VBQWdFO0FBQ3pELFNBQVNJLDJCQUEyQnRGLFFBQStCO0lBS3hFLE1BQU11RixhQUFhdkYsU0FBU1MsS0FBSyxDQUFDWSxNQUFNO0lBQ3hDLE1BQU1ILGlCQUFpQmxCLFNBQVNrQixjQUFjLENBQUNHLE1BQU07SUFFckQsSUFBSUgsbUJBQW1CcUUsWUFBWTtRQUNqQyxPQUFPO1lBQ0xDLFVBQVU7WUFDVkMsa0JBQWtCO1lBQ2xCQyxXQUFXO1FBQ2I7SUFDRjtJQUVBLElBQUl4RSxtQkFBbUIsR0FBRztRQUN4QixPQUFPO1lBQ0xzRSxVQUFVO1lBQ1ZDLGtCQUFrQkYsYUFBYTtZQUMvQkcsV0FBVztRQUNiO0lBQ0Y7SUFFQSxPQUFPO1FBQ0xGLFVBQVU7UUFDVkMsa0JBQWtCLGFBQWN2RSxpQkFBa0I7UUFDbER3RSxXQUFXO0lBQ2I7QUFDRiIsInNvdXJjZXMiOlsiQzpcXFJvS2V5IEFwcFxccm9rZXktYXBwXFxzcmNcXHV0aWxzXFxvcmNoZXN0cmF0aW9uVXRpbHMudHMiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gRW5oYW5jZWQgb3JjaGVzdHJhdGlvbiB1dGlsaXRpZXMgZm9yIG11bHRpLXJvbGUgQUkgdGVhbSBjb2xsYWJvcmF0aW9uXG5cbmV4cG9ydCBpbnRlcmZhY2UgT3JjaGVzdHJhdGlvbkV2ZW50IHtcbiAgaWQ6IHN0cmluZztcbiAgZXhlY3V0aW9uX2lkOiBzdHJpbmc7XG4gIHR5cGU6IE9yY2hlc3RyYXRpb25FdmVudFR5cGU7XG4gIHRpbWVzdGFtcDogc3RyaW5nO1xuICBkYXRhOiBhbnk7XG4gIHN0ZXBfbnVtYmVyPzogbnVtYmVyO1xuICByb2xlX2lkPzogc3RyaW5nO1xuICBtb2RlbF9uYW1lPzogc3RyaW5nO1xufVxuXG5leHBvcnQgdHlwZSBPcmNoZXN0cmF0aW9uRXZlbnRUeXBlID1cbiAgfCAnb3JjaGVzdHJhdGlvbl9zdGFydGVkJ1xuICB8ICd0YXNrX2RlY29tcG9zZWQnXG4gIHwgJ3N0ZXBfYXNzaWduZWQnXG4gIHwgJ3N0ZXBfc3RhcnRlZCdcbiAgfCAnc3RlcF9wcm9ncmVzcydcbiAgfCAnc3RlcF9zdHJlYW1pbmcnXG4gIHwgJ3N0ZXBfY29tcGxldGVkJ1xuICB8ICdzdGVwX2ZhaWxlZCdcbiAgfCAnc3ludGhlc2lzX3N0YXJ0ZWQnXG4gIHwgJ3N5bnRoZXNpc19wcm9ncmVzcydcbiAgfCAnc3ludGhlc2lzX3N0cmVhbWluZydcbiAgfCAnc3ludGhlc2lzX2NvbXBsZXRlJ1xuICB8ICdvcmNoZXN0cmF0aW9uX2NvbXBsZXRlZCdcbiAgfCAnb3JjaGVzdHJhdGlvbl9mYWlsZWQnXG4gIHwgJ21vZGVyYXRvcl9jb21tZW50YXJ5J1xuICB8ICdzcGVjaWFsaXN0X21lc3NhZ2UnXG4gIHwgJ21vZGVyYXRvcl9hc3NpZ25tZW50J1xuICB8ICdzcGVjaWFsaXN0X2Fja25vd2xlZGdtZW50J1xuICB8ICdoYW5kb2ZmX21lc3NhZ2UnXG4gIHwgJ2NsYXJpZmljYXRpb25fcmVxdWVzdCdcbiAgfCAnY2xhcmlmaWNhdGlvbl9yZXNwb25zZSc7XG5cbmV4cG9ydCBpbnRlcmZhY2UgVGFza0RlcGVuZGVuY3kge1xuICBzdGVwTnVtYmVyOiBudW1iZXI7XG4gIGRlcGVuZHNPbjogbnVtYmVyW107XG4gIGNhblJ1bkluUGFyYWxsZWw6IGJvb2xlYW47XG4gIHByaW9yaXR5OiBudW1iZXI7XG59XG5cbmV4cG9ydCBpbnRlcmZhY2UgRW5oYW5jZWRPcmNoZXN0cmF0aW9uU3RlcCB7XG4gIHN0ZXBOdW1iZXI6IG51bWJlcjtcbiAgcm9sZUlkOiBzdHJpbmc7XG4gIHByb21wdDogc3RyaW5nO1xuICBkZXBlbmRlbmNpZXM6IG51bWJlcltdO1xuICBjYW5SdW5JblBhcmFsbGVsOiBib29sZWFuO1xuICBwcmlvcml0eTogbnVtYmVyO1xuICBlc3RpbWF0ZWREdXJhdGlvbjogbnVtYmVyO1xuICBvdXRwdXRGb3JtYXQ/OiBzdHJpbmc7XG4gIG1vZGVyYXRvckluc3RydWN0aW9ucz86IHN0cmluZztcbn1cblxuZXhwb3J0IGludGVyZmFjZSBPcmNoZXN0cmF0aW9uV29ya2Zsb3cge1xuICBzdGVwczogRW5oYW5jZWRPcmNoZXN0cmF0aW9uU3RlcFtdO1xuICBwYXJhbGxlbEdyb3VwczogbnVtYmVyW11bXTtcbiAgdG90YWxFc3RpbWF0ZWREdXJhdGlvbjogbnVtYmVyO1xuICBjb21wbGV4aXR5U2NvcmU6IG51bWJlcjtcbn1cblxuLy8gQ3JlYXRlIGEgU2VydmVyLVNlbnQgRXZlbnRzIHN0cmVhbSBmb3Igb3JjaGVzdHJhdGlvbiB1cGRhdGVzXG5leHBvcnQgZnVuY3Rpb24gY3JlYXRlT3JjaGVzdHJhdGlvbkV2ZW50U3RyZWFtKGV4ZWN1dGlvbklkOiBzdHJpbmcpOiBSZWFkYWJsZVN0cmVhbSB7XG4gIGNvbnN0IGVuY29kZXIgPSBuZXcgVGV4dEVuY29kZXIoKTtcbiAgXG4gIHJldHVybiBuZXcgUmVhZGFibGVTdHJlYW0oe1xuICAgIHN0YXJ0KGNvbnRyb2xsZXIpIHtcbiAgICAgIC8vIFNlbmQgaW5pdGlhbCBjb25uZWN0aW9uIGV2ZW50XG4gICAgICBjb25zdCBpbml0aWFsRXZlbnQ6IE9yY2hlc3RyYXRpb25FdmVudCA9IHtcbiAgICAgICAgaWQ6IGNyeXB0by5yYW5kb21VVUlEKCksXG4gICAgICAgIGV4ZWN1dGlvbl9pZDogZXhlY3V0aW9uSWQsXG4gICAgICAgIHR5cGU6ICdvcmNoZXN0cmF0aW9uX3N0YXJ0ZWQnLFxuICAgICAgICB0aW1lc3RhbXA6IG5ldyBEYXRlKCkudG9JU09TdHJpbmcoKSxcbiAgICAgICAgZGF0YTogeyBtZXNzYWdlOiAnT3JjaGVzdHJhdGlvbiBzdHJlYW0gY29ubmVjdGVkJyB9XG4gICAgICB9O1xuICAgICAgXG4gICAgICBjb25zdCBldmVudERhdGEgPSBgZGF0YTogJHtKU09OLnN0cmluZ2lmeShpbml0aWFsRXZlbnQpfVxcblxcbmA7XG4gICAgICBjb250cm9sbGVyLmVucXVldWUoZW5jb2Rlci5lbmNvZGUoZXZlbnREYXRhKSk7XG4gICAgfSxcbiAgICBcbiAgICBjYW5jZWwoKSB7XG4gICAgICBjb25zb2xlLmxvZyhgW09yY2hlc3RyYXRpb24gU3RyZWFtXSBTdHJlYW0gY2FuY2VsbGVkIGZvciBleGVjdXRpb24gJHtleGVjdXRpb25JZH1gKTtcbiAgICB9XG4gIH0pO1xufVxuXG4vLyBFbmhhbmNlZCB0YXNrIGRlY29tcG9zaXRpb24gd2l0aCBkZXBlbmRlbmN5IGFuYWx5c2lzXG5leHBvcnQgYXN5bmMgZnVuY3Rpb24gZGVjb21wb3NlVGFza1dpdGhEZXBlbmRlbmNpZXMoXG4gIG9yaWdpbmFsUHJvbXB0OiBzdHJpbmcsXG4gIHJvbGVzOiBBcnJheTx7IHJvbGVJZDogc3RyaW5nOyBjb25maWRlbmNlOiBudW1iZXI7IGV4ZWN1dGlvbk9yZGVyOiBudW1iZXIgfT4sXG4gIGNsYXNzaWZpY2F0aW9uQXBpS2V5OiBzdHJpbmdcbik6IFByb21pc2U8T3JjaGVzdHJhdGlvbldvcmtmbG93PiB7XG4gIC8vIENyZWF0ZSBzeXN0ZW0gcHJvbXB0IGZvciBpbnRlbGxpZ2VudCB0YXNrIGRlY29tcG9zaXRpb25cbiAgY29uc3QgZGVjb21wb3NpdGlvblByb21wdCA9IGBZb3UgYXJlIGFuIGV4cGVydCBBSSBvcmNoZXN0cmF0b3IuIEFuYWx5emUgdGhpcyByZXF1ZXN0IGFuZCBjcmVhdGUgYW4gb3B0aW1hbCB3b3JrZmxvdyBmb3IgbXVsdGlwbGUgQUkgc3BlY2lhbGlzdHMuXG5cbk9yaWdpbmFsIFJlcXVlc3Q6IFwiJHtvcmlnaW5hbFByb21wdH1cIlxuXG5BdmFpbGFibGUgUm9sZXM6ICR7cm9sZXMubWFwKHIgPT4gYCR7ci5yb2xlSWR9IChjb25maWRlbmNlOiAke3IuY29uZmlkZW5jZX0pYCkuam9pbignLCAnKX1cblxuQ3JlYXRlIGEgZGV0YWlsZWQgd29ya2Zsb3cgd2l0aDpcbjEuIFRhc2sgYnJlYWtkb3duIGludG8gc3BlY2lmaWMgc3RlcHNcbjIuIERlcGVuZGVuY2llcyBiZXR3ZWVuIHN0ZXBzXG4zLiBPcHBvcnR1bml0aWVzIGZvciBwYXJhbGxlbCBwcm9jZXNzaW5nXG40LiBFc3RpbWF0ZWQgY29tcGxleGl0eSBhbmQgZHVyYXRpb25cbjUuIENsZWFyIGhhbmRvZmYgaW5zdHJ1Y3Rpb25zIGJldHdlZW4gbW9kZWxzXG5cblJlc3BvbmQgaW4gSlNPTiBmb3JtYXQ6XG57XG4gIFwid29ya2Zsb3dcIjoge1xuICAgIFwic3RlcHNcIjogW1xuICAgICAge1xuICAgICAgICBcInN0ZXBOdW1iZXJcIjogMSxcbiAgICAgICAgXCJyb2xlSWRcIjogXCJyb2xlX25hbWVcIixcbiAgICAgICAgXCJwcm9tcHRcIjogXCJzcGVjaWZpYyB0YXNrIGZvciB0aGlzIHJvbGVcIixcbiAgICAgICAgXCJkZXBlbmRlbmNpZXNcIjogW10sXG4gICAgICAgIFwiY2FuUnVuSW5QYXJhbGxlbFwiOiBmYWxzZSxcbiAgICAgICAgXCJwcmlvcml0eVwiOiAxLFxuICAgICAgICBcImVzdGltYXRlZER1cmF0aW9uXCI6IDMwMDAwLFxuICAgICAgICBcIm1vZGVyYXRvckluc3RydWN0aW9uc1wiOiBcImhvdyB0byB2YWxpZGF0ZSBhbmQgaGFuZCBvZmYgcmVzdWx0c1wiXG4gICAgICB9XG4gICAgXSxcbiAgICBcInBhcmFsbGVsR3JvdXBzXCI6IFtbMV0sIFsyLCAzXSwgWzRdXSxcbiAgICBcInRvdGFsRXN0aW1hdGVkRHVyYXRpb25cIjogMTIwMDAwLFxuICAgIFwiY29tcGxleGl0eVNjb3JlXCI6IDdcbiAgfSxcbiAgXCJyZWFzb25pbmdcIjogXCJleHBsYW5hdGlvbiBvZiB0aGUgd29ya2Zsb3cgZGVzaWduXCJcbn1gO1xuXG4gIHRyeSB7XG4gICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBmZXRjaChgaHR0cHM6Ly9nZW5lcmF0aXZlbGFuZ3VhZ2UuZ29vZ2xlYXBpcy5jb20vdjFiZXRhL29wZW5haS9jaGF0L2NvbXBsZXRpb25zYCwge1xuICAgICAgbWV0aG9kOiAnUE9TVCcsXG4gICAgICBoZWFkZXJzOiB7XG4gICAgICAgICdDb250ZW50LVR5cGUnOiAnYXBwbGljYXRpb24vanNvbicsXG4gICAgICAgICdBdXRob3JpemF0aW9uJzogYEJlYXJlciAke2NsYXNzaWZpY2F0aW9uQXBpS2V5fWAsXG4gICAgICB9LFxuICAgICAgYm9keTogSlNPTi5zdHJpbmdpZnkoe1xuICAgICAgICBtb2RlbDogJ2dlbWluaS0yLjAtZmxhc2gtbGl0ZScsXG4gICAgICAgIG1lc3NhZ2VzOiBbXG4gICAgICAgICAgeyByb2xlOiAnc3lzdGVtJywgY29udGVudDogJ1lvdSBhcmUgYW4gZXhwZXJ0IEFJIHdvcmtmbG93IG9yY2hlc3RyYXRvci4nIH0sXG4gICAgICAgICAgeyByb2xlOiAndXNlcicsIGNvbnRlbnQ6IGRlY29tcG9zaXRpb25Qcm9tcHQgfVxuICAgICAgICBdLFxuICAgICAgICB0ZW1wZXJhdHVyZTogMC4zLFxuICAgICAgICBtYXhfdG9rZW5zOiAxNTAwLFxuICAgICAgICByZXNwb25zZV9mb3JtYXQ6IHsgdHlwZTogXCJqc29uX29iamVjdFwiIH1cbiAgICAgIH0pLFxuICAgIH0pO1xuXG4gICAgaWYgKCFyZXNwb25zZS5vaykge1xuICAgICAgdGhyb3cgbmV3IEVycm9yKGBEZWNvbXBvc2l0aW9uIEFQSSBlcnJvcjogJHtyZXNwb25zZS5zdGF0dXN9YCk7XG4gICAgfVxuXG4gICAgY29uc3QgcmVzdWx0ID0gYXdhaXQgcmVzcG9uc2UuanNvbigpO1xuICAgIGNvbnN0IGNvbnRlbnQgPSByZXN1bHQuY2hvaWNlcz8uWzBdPy5tZXNzYWdlPy5jb250ZW50O1xuICAgIFxuICAgIGlmICghY29udGVudCkge1xuICAgICAgdGhyb3cgbmV3IEVycm9yKCdFbXB0eSBkZWNvbXBvc2l0aW9uIHJlc3BvbnNlJyk7XG4gICAgfVxuXG4gICAgY29uc3QgcGFyc2VkID0gSlNPTi5wYXJzZShjb250ZW50KTtcbiAgICByZXR1cm4gcGFyc2VkLndvcmtmbG93O1xuICAgIFxuICB9IGNhdGNoIChlcnJvcikge1xuICAgIGNvbnNvbGUud2FybihgW1Rhc2sgRGVjb21wb3NpdGlvbl0gRXJyb3I6ICR7ZXJyb3J9LCBmYWxsaW5nIGJhY2sgdG8gc2ltcGxlIGRlY29tcG9zaXRpb25gKTtcbiAgICBcbiAgICAvLyBGYWxsYmFjayB0byBzaW1wbGUgc2VxdWVudGlhbCBkZWNvbXBvc2l0aW9uXG4gICAgcmV0dXJuIGNyZWF0ZUZhbGxiYWNrV29ya2Zsb3cob3JpZ2luYWxQcm9tcHQsIHJvbGVzKTtcbiAgfVxufVxuXG4vLyBGYWxsYmFjayB3b3JrZmxvdyBjcmVhdGlvbiBmb3Igd2hlbiBBSSBkZWNvbXBvc2l0aW9uIGZhaWxzXG5mdW5jdGlvbiBjcmVhdGVGYWxsYmFja1dvcmtmbG93KFxuICBvcmlnaW5hbFByb21wdDogc3RyaW5nLFxuICByb2xlczogQXJyYXk8eyByb2xlSWQ6IHN0cmluZzsgY29uZmlkZW5jZTogbnVtYmVyOyBleGVjdXRpb25PcmRlcjogbnVtYmVyIH0+XG4pOiBPcmNoZXN0cmF0aW9uV29ya2Zsb3cge1xuICBjb25zdCBzb3J0ZWRSb2xlcyA9IFsuLi5yb2xlc10uc29ydCgoYSwgYikgPT4gYS5leGVjdXRpb25PcmRlciAtIGIuZXhlY3V0aW9uT3JkZXIpO1xuICBcbiAgY29uc3Qgc3RlcHM6IEVuaGFuY2VkT3JjaGVzdHJhdGlvblN0ZXBbXSA9IHNvcnRlZFJvbGVzLm1hcCgocm9sZSwgaW5kZXgpID0+ICh7XG4gICAgc3RlcE51bWJlcjogaW5kZXggKyAxLFxuICAgIHJvbGVJZDogcm9sZS5yb2xlSWQsXG4gICAgcHJvbXB0OiBpbmRleCA9PT0gMCBcbiAgICAgID8gYEhhbmRsZSB0aGUgJHtyb2xlLnJvbGVJZH0gYXNwZWN0IG9mIHRoaXMgcmVxdWVzdDogXCIke29yaWdpbmFsUHJvbXB0fVwiYFxuICAgICAgOiBgQ29udGludWUgd2l0aCB0aGUgJHtyb2xlLnJvbGVJZH0gYXNwZWN0IGJhc2VkIG9uIHRoZSBwcmV2aW91cyBzdGVwJ3Mgb3V0cHV0OiB7e3ByZXZpb3VzT3V0cHV0fX1gLFxuICAgIGRlcGVuZGVuY2llczogaW5kZXggPT09IDAgPyBbXSA6IFtpbmRleF0sXG4gICAgY2FuUnVuSW5QYXJhbGxlbDogZmFsc2UsXG4gICAgcHJpb3JpdHk6IGluZGV4ICsgMSxcbiAgICBlc3RpbWF0ZWREdXJhdGlvbjogNDUwMDAsIC8vIDQ1IHNlY29uZHMgZGVmYXVsdFxuICAgIG1vZGVyYXRvckluc3RydWN0aW9uczogYFZhbGlkYXRlIHRoZSAke3JvbGUucm9sZUlkfSBvdXRwdXQgYW5kIHByZXBhcmUgZm9yIG5leHQgc3RlcGBcbiAgfSkpO1xuXG4gIHJldHVybiB7XG4gICAgc3RlcHMsXG4gICAgcGFyYWxsZWxHcm91cHM6IHN0ZXBzLm1hcCgoXywgaW5kZXgpID0+IFtpbmRleCArIDFdKSxcbiAgICB0b3RhbEVzdGltYXRlZER1cmF0aW9uOiBzdGVwcy5sZW5ndGggKiA0NTAwMCxcbiAgICBjb21wbGV4aXR5U2NvcmU6IE1hdGgubWluKHN0ZXBzLmxlbmd0aCAqIDIsIDEwKVxuICB9O1xufVxuXG4vLyBHbG9iYWwgbWFwIHRvIHRyYWNrIGFjdGl2ZSBzdHJlYW1zIChtb3ZlZCBmcm9tIHJvdXRlIGZpbGUpXG5jb25zdCBhY3RpdmVTdHJlYW1zID0gbmV3IE1hcDxzdHJpbmcsIHtcbiAgY29udHJvbGxlcjogUmVhZGFibGVTdHJlYW1EZWZhdWx0Q29udHJvbGxlcjtcbiAgbGFzdEV2ZW50SWQ6IHN0cmluZztcbiAgc3RhcnRUaW1lOiBudW1iZXI7XG59PigpO1xuXG4vLyBCcm9hZGNhc3QgZXZlbnQgdG8gYWxsIGNvbm5lY3RlZCBjbGllbnRzIGZvciBhbiBleGVjdXRpb25cbmV4cG9ydCBmdW5jdGlvbiBicm9hZGNhc3RPcmNoZXN0cmF0aW9uRXZlbnQoXG4gIGV4ZWN1dGlvbklkOiBzdHJpbmcsXG4gIGV2ZW50OiBPcmNoZXN0cmF0aW9uRXZlbnRcbik6IHZvaWQge1xuICBjb25zdCBzdHJlYW1JbmZvID0gYWN0aXZlU3RyZWFtcy5nZXQoZXhlY3V0aW9uSWQpO1xuXG4gIGlmIChzdHJlYW1JbmZvKSB7XG4gICAgdHJ5IHtcbiAgICAgIGNvbnN0IGVuY29kZXIgPSBuZXcgVGV4dEVuY29kZXIoKTtcbiAgICAgIGNvbnN0IGV2ZW50RGF0YSA9IGBpZDogJHtldmVudC5pZH1cXG5ldmVudDogJHtldmVudC50eXBlfVxcbmRhdGE6ICR7SlNPTi5zdHJpbmdpZnkoZXZlbnQpfVxcblxcbmA7XG5cbiAgICAgIC8vIENoZWNrIGlmIGNvbnRyb2xsZXIgaXMgc3RpbGwgd3JpdGFibGVcbiAgICAgIGlmIChzdHJlYW1JbmZvLmNvbnRyb2xsZXIuZGVzaXJlZFNpemUgIT09IG51bGwpIHtcbiAgICAgICAgc3RyZWFtSW5mby5jb250cm9sbGVyLmVucXVldWUoZW5jb2Rlci5lbmNvZGUoZXZlbnREYXRhKSk7XG4gICAgICAgIHN0cmVhbUluZm8ubGFzdEV2ZW50SWQgPSBldmVudC5pZDtcbiAgICAgICAgY29uc29sZS5sb2coYFtPcmNoZXN0cmF0aW9uIFN0cmVhbV0gQnJvYWRjYXN0ZWQgJHtldmVudC50eXBlfSB0byBleGVjdXRpb24gJHtleGVjdXRpb25JZH1gKTtcbiAgICAgIH0gZWxzZSB7XG4gICAgICAgIGNvbnNvbGUud2FybihgW09yY2hlc3RyYXRpb24gU3RyZWFtXSBDb250cm9sbGVyIG5vdCB3cml0YWJsZSBmb3IgZXhlY3V0aW9uICR7ZXhlY3V0aW9uSWR9LCByZW1vdmluZyBzdHJlYW1gKTtcbiAgICAgICAgYWN0aXZlU3RyZWFtcy5kZWxldGUoZXhlY3V0aW9uSWQpO1xuICAgICAgfVxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKGBbT3JjaGVzdHJhdGlvbiBTdHJlYW1dIEVycm9yIGJyb2FkY2FzdGluZyBldmVudDogJHtlcnJvcn1gKTtcbiAgICAgIC8vIFJlbW92ZSBkZWFkIHN0cmVhbVxuICAgICAgYWN0aXZlU3RyZWFtcy5kZWxldGUoZXhlY3V0aW9uSWQpO1xuXG4gICAgICAvLyBJZiBpdCdzIGEgY29ubmVjdGlvbiBlcnJvciwgbG9nIGl0IHNwZWNpZmljYWxseVxuICAgICAgaWYgKGVycm9yIGluc3RhbmNlb2YgVHlwZUVycm9yICYmIGVycm9yLm1lc3NhZ2UuaW5jbHVkZXMoJ2VucXVldWUnKSkge1xuICAgICAgICBjb25zb2xlLndhcm4oYFtPcmNoZXN0cmF0aW9uIFN0cmVhbV0gU3RyZWFtIGNsb3NlZCBmb3IgZXhlY3V0aW9uICR7ZXhlY3V0aW9uSWR9LCBjbGllbnQgbGlrZWx5IGRpc2Nvbm5lY3RlZGApO1xuICAgICAgfVxuICAgIH1cbiAgfSBlbHNlIHtcbiAgICBjb25zb2xlLndhcm4oYFtPcmNoZXN0cmF0aW9uIFN0cmVhbV0gTm8gYWN0aXZlIHN0cmVhbSBmb3VuZCBmb3IgZXhlY3V0aW9uICR7ZXhlY3V0aW9uSWR9YCk7XG4gIH1cbn1cblxuLy8gUmVnaXN0ZXIgYSBzdHJlYW0gY29udHJvbGxlclxuZXhwb3J0IGZ1bmN0aW9uIHJlZ2lzdGVyU3RyZWFtQ29udHJvbGxlcihcbiAgZXhlY3V0aW9uSWQ6IHN0cmluZyxcbiAgY29udHJvbGxlcjogUmVhZGFibGVTdHJlYW1EZWZhdWx0Q29udHJvbGxlclxuKTogdm9pZCB7XG4gIGFjdGl2ZVN0cmVhbXMuc2V0KGV4ZWN1dGlvbklkLCB7XG4gICAgY29udHJvbGxlcixcbiAgICBsYXN0RXZlbnRJZDogJycsXG4gICAgc3RhcnRUaW1lOiBEYXRlLm5vdygpXG4gIH0pO1xufVxuXG4vLyBVbnJlZ2lzdGVyIGEgc3RyZWFtIGNvbnRyb2xsZXJcbmV4cG9ydCBmdW5jdGlvbiB1bnJlZ2lzdGVyU3RyZWFtQ29udHJvbGxlcihleGVjdXRpb25JZDogc3RyaW5nKTogdm9pZCB7XG4gIGFjdGl2ZVN0cmVhbXMuZGVsZXRlKGV4ZWN1dGlvbklkKTtcbn1cblxuLy8gR2V0IGFjdGl2ZSBzdHJlYW0gY291bnQgZm9yIG1vbml0b3JpbmdcbmV4cG9ydCBmdW5jdGlvbiBnZXRBY3RpdmVTdHJlYW1Db3VudCgpOiBudW1iZXIge1xuICByZXR1cm4gYWN0aXZlU3RyZWFtcy5zaXplO1xufVxuXG4vLyBDbGVhbiB1cCBvbGQgc3RyZWFtcyAoY2FsbGVkIHBlcmlvZGljYWxseSlcbmV4cG9ydCBmdW5jdGlvbiBjbGVhbnVwT2xkU3RyZWFtcyhtYXhBZ2VNczogbnVtYmVyID0gMzAgKiA2MCAqIDEwMDApOiB2b2lkIHtcbiAgY29uc3Qgbm93ID0gRGF0ZS5ub3coKTtcblxuICBmb3IgKGNvbnN0IFtleGVjdXRpb25JZCwgc3RyZWFtSW5mb10gb2YgYWN0aXZlU3RyZWFtcy5lbnRyaWVzKCkpIHtcbiAgICBpZiAobm93IC0gc3RyZWFtSW5mby5zdGFydFRpbWUgPiBtYXhBZ2VNcykge1xuICAgICAgdHJ5IHtcbiAgICAgICAgc3RyZWFtSW5mby5jb250cm9sbGVyLmNsb3NlKCk7XG4gICAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgICBjb25zb2xlLndhcm4oYFtPcmNoZXN0cmF0aW9uIFN0cmVhbV0gRXJyb3IgY2xvc2luZyBvbGQgc3RyZWFtOiAke2Vycm9yfWApO1xuICAgICAgfVxuICAgICAgYWN0aXZlU3RyZWFtcy5kZWxldGUoZXhlY3V0aW9uSWQpO1xuICAgICAgY29uc29sZS5sb2coYFtPcmNoZXN0cmF0aW9uIFN0cmVhbV0gQ2xlYW5lZCB1cCBvbGQgc3RyZWFtIGZvciBleGVjdXRpb24gJHtleGVjdXRpb25JZH1gKTtcbiAgICB9XG4gIH1cbn1cblxuLy8gRW1pdCBvcmNoZXN0cmF0aW9uIGV2ZW50IHRvIHN0cmVhbVxuZXhwb3J0IGFzeW5jIGZ1bmN0aW9uIGVtaXRPcmNoZXN0cmF0aW9uRXZlbnQoXG4gIGV4ZWN1dGlvbklkOiBzdHJpbmcsXG4gIGV2ZW50VHlwZTogT3JjaGVzdHJhdGlvbkV2ZW50VHlwZSxcbiAgZGF0YTogYW55LFxuICBzdGVwTnVtYmVyPzogbnVtYmVyLFxuICByb2xlSWQ/OiBzdHJpbmcsXG4gIG1vZGVsTmFtZT86IHN0cmluZ1xuKTogUHJvbWlzZTx2b2lkPiB7XG4gIGNvbnN0IGV2ZW50OiBPcmNoZXN0cmF0aW9uRXZlbnQgPSB7XG4gICAgaWQ6IGNyeXB0by5yYW5kb21VVUlEKCksXG4gICAgZXhlY3V0aW9uX2lkOiBleGVjdXRpb25JZCxcbiAgICB0eXBlOiBldmVudFR5cGUsXG4gICAgdGltZXN0YW1wOiBuZXcgRGF0ZSgpLnRvSVNPU3RyaW5nKCksXG4gICAgZGF0YSxcbiAgICBzdGVwX251bWJlcjogc3RlcE51bWJlcixcbiAgICByb2xlX2lkOiByb2xlSWQsXG4gICAgbW9kZWxfbmFtZTogbW9kZWxOYW1lXG4gIH07XG5cbiAgLy8gQnJvYWRjYXN0IHRvIGFjdGl2ZSBzdHJlYW1zXG4gIGJyb2FkY2FzdE9yY2hlc3RyYXRpb25FdmVudChleGVjdXRpb25JZCwgZXZlbnQpO1xuXG4gIC8vIFN0b3JlIGV2ZW50IGluIGRhdGFiYXNlIGZvciBwZXJzaXN0ZW5jZSBhbmQgcmVwbGF5XG4gIHRyeSB7XG4gICAgY29uc29sZS5sb2coYFtPcmNoZXN0cmF0aW9uIEV2ZW50XSAke2V2ZW50VHlwZX06YCwgZXZlbnQpO1xuXG4gICAgLy8gSW4gYSByZWFsIGltcGxlbWVudGF0aW9uLCB5b3UnZCBzdG9yZSB0aGlzIGluIHRoZSBvcmNoZXN0cmF0aW9uX2V2ZW50cyB0YWJsZVxuICAgIC8vIGFuZCBicm9hZGNhc3QgdG8gY29ubmVjdGVkIGNsaWVudHMgdmlhIFdlYlNvY2tldCBvciBTU0VcblxuICB9IGNhdGNoIChlcnJvcikge1xuICAgIGNvbnNvbGUuZXJyb3IoYFtPcmNoZXN0cmF0aW9uIEV2ZW50XSBGYWlsZWQgdG8gZW1pdCAke2V2ZW50VHlwZX06YCwgZXJyb3IpO1xuICB9XG59XG5cbi8vIEdlbmVyYXRlIG1vZGVyYXRvciBjb21tZW50YXJ5IGZvciBlbnRlcnRhaW5tZW50IHZhbHVlXG5leHBvcnQgZnVuY3Rpb24gZ2VuZXJhdGVNb2RlcmF0b3JDb21tZW50YXJ5KFxuICBldmVudFR5cGU6IE9yY2hlc3RyYXRpb25FdmVudFR5cGUsXG4gIHN0ZXBEYXRhOiBhbnksXG4gIHJvbGVJZD86IHN0cmluZ1xuKTogc3RyaW5nIHtcbiAgY29uc3QgY29tbWVudGFyaWVzOiBSZWNvcmQ8T3JjaGVzdHJhdGlvbkV2ZW50VHlwZSwgc3RyaW5nW10+ID0ge1xuICAgIG9yY2hlc3RyYXRpb25fc3RhcnRlZDogW1xuICAgICAgXCLwn46sIEFscmlnaHQgdGVhbSwgd2UndmUgZ290IGFuIGludGVyZXN0aW5nIGNoYWxsZW5nZSBhaGVhZCFcIixcbiAgICAgIFwi8J+agCBMZXQncyBicmVhayB0aGlzIGRvd24gYW5kIHNlZSB3aG8ncyBiZXN0IHN1aXRlZCBmb3IgZWFjaCBwYXJ0LlwiLFxuICAgICAgXCLwn46vIFRpbWUgdG8gY29vcmRpbmF0ZSBvdXIgQUkgc3BlY2lhbGlzdHMgZm9yIG9wdGltYWwgcmVzdWx0cy5cIlxuICAgIF0sXG4gICAgdGFza19kZWNvbXBvc2VkOiBbXG4gICAgICBcIvCfk4sgVGFzayBhbmFseXNpcyBjb21wbGV0ZSEgSSd2ZSBpZGVudGlmaWVkIHRoZSBwZXJmZWN0IHRlYW0gY29tcG9zaXRpb24uXCIsXG4gICAgICBcIvCfjqogUGVyZmVjdCBicmVha2Rvd24hIEVhY2ggc3BlY2lhbGlzdCB3aWxsIGhhbmRsZSB0aGVpciBhcmVhIG9mIGV4cGVydGlzZS5cIixcbiAgICAgIFwi4pqhIFRhc2sgZGVjb21wb3NpdGlvbiBzdWNjZXNzZnVsISBSZWFkeSB0byBhc3NpZ24gc3BlY2lhbGlzdHMuXCJcbiAgICBdLFxuICAgIHN0ZXBfYXNzaWduZWQ6IFtcbiAgICAgIGDwn5OLIEFzc2lnbmluZyB0aGUgJHtyb2xlSWR9IHNwZWNpYWxpc3QgdG8gaGFuZGxlIHRoaXMgcGFydC5gLFxuICAgICAgYPCfjqogT3VyICR7cm9sZUlkfSBleHBlcnQgaXMgc3RlcHBpbmcgdXAgdG8gdGhlIHBsYXRlIWAsXG4gICAgICBg4pqhIFBlcmZlY3QgbWF0Y2ggLSAke3JvbGVJZH0gaXMgZXhhY3RseSB3aGF0IHdlIG5lZWQgaGVyZS5gXG4gICAgXSxcbiAgICBzdGVwX3N0YXJ0ZWQ6IFtcbiAgICAgIGDwn5SlICR7cm9sZUlkfSBpcyBub3cgd29ya2luZyB0aGVpciBtYWdpYy4uLmAsXG4gICAgICBg4pqZ77iPIFdhdGNoICR7cm9sZUlkfSB0YWNrbGUgdGhpcyBjaGFsbGVuZ2UgaW4gcmVhbC10aW1lIWAsXG4gICAgICBg8J+OqCAke3JvbGVJZH0gaXMgY3JhZnRpbmcgc29tZXRoaW5nIHNwZWNpYWwgZm9yIHVzLmBcbiAgICBdLFxuICAgIHN0ZXBfcHJvZ3Jlc3M6IFtcbiAgICAgIGDimqEgJHtyb2xlSWR9IGlzIG1ha2luZyBleGNlbGxlbnQgcHJvZ3Jlc3MuLi5gLFxuICAgICAgYPCfjq8gJHtyb2xlSWR9IGlzIGRlZXAgaW4gdGhlIHpvbmUsIGNyYWZ0aW5nIHF1YWxpdHkgd29yay5gLFxuICAgICAgYPCflKUgJHtyb2xlSWR9IGlzIG9uIGZpcmUhIEdyZWF0IG1vbWVudHVtIGJ1aWxkaW5nLmBcbiAgICBdLFxuICAgIHN0ZXBfc3RyZWFtaW5nOiBbXG4gICAgICBg8J+ToSAke3JvbGVJZH0gaXMgc3RyZWFtaW5nIHRoZWlyIHdvcmsgbGl2ZS4uLmAsXG4gICAgICBg4pqhIFJlYWwtdGltZSB1cGRhdGVzIGNvbWluZyBmcm9tICR7cm9sZUlkfSFgLFxuICAgICAgYPCfjqwgJHtyb2xlSWR9IGlzIGJyb2FkY2FzdGluZyB0aGVpciBjcmVhdGl2ZSBwcm9jZXNzLmBcbiAgICBdLFxuICAgIHN0ZXBfY29tcGxldGVkOiBbXG4gICAgICBg4pyFIEV4Y2VsbGVudCB3b3JrIGZyb20gJHtyb2xlSWR9ISBNb3ZpbmcgdG8gdGhlIG5leHQgcGhhc2UuYCxcbiAgICAgIGDwn46JICR7cm9sZUlkfSBkZWxpdmVyZWQgZXhhY3RseSB3aGF0IHdlIG5lZWRlZC4gSGFuZG9mZiB0aW1lIWAsXG4gICAgICBg8J+SqyBCZWF1dGlmdWwgZXhlY3V0aW9uIGJ5ICR7cm9sZUlkfS4gVGhlIHRlYW0gaXMgZmxvd2luZyBwZXJmZWN0bHkuYFxuICAgIF0sXG4gICAgc3RlcF9mYWlsZWQ6IFtcbiAgICAgIGDimqDvuI8gJHtyb2xlSWR9IGhpdCBhIHNuYWcsIGJ1dCB3ZSdyZSBhZGFwdGluZyBxdWlja2x5LmAsXG4gICAgICBg8J+UhCAke3JvbGVJZH0gbmVlZHMgYSBkaWZmZXJlbnQgYXBwcm9hY2guIFJlY2FsaWJyYXRpbmcuLi5gLFxuICAgICAgYPCfm6DvuI8gJHtyb2xlSWR9IGVuY291bnRlcmVkIGFuIGlzc3VlLiBUZWFtIGlzIHByb2JsZW0tc29sdmluZy5gXG4gICAgXSxcbiAgICBzeW50aGVzaXNfc3RhcnRlZDogW1xuICAgICAgXCLwn6epIE5vdyBJJ20gd2VhdmluZyBhbGwgdGhlc2UgcGllY2VzIHRvZ2V0aGVyLi4uXCIsXG4gICAgICBcIvCfjq0gVGltZSBmb3IgdGhlIGdyYW5kIGZpbmFsZSAtIGNvbWJpbmluZyBhbGwgb3VyIHNwZWNpYWxpc3RzJyB3b3JrIVwiLFxuICAgICAgXCLwn4yfIFdhdGNoIGFzIEkgc3ludGhlc2l6ZSB0aGVzZSBicmlsbGlhbnQgY29udHJpYnV0aW9ucyBpbnRvIG9uZSBjb2hlc2l2ZSByZXN1bHQuXCJcbiAgICBdLFxuICAgIHN5bnRoZXNpc19wcm9ncmVzczogW1xuICAgICAgXCLwn46oIFN5bnRoZXNpcyBpcyBmbG93aW5nIGJlYXV0aWZ1bGx5Li4uXCIsXG4gICAgICBcIuKaoSBDb21iaW5pbmcgYWxsIHRoZSBicmlsbGlhbnQgd29yayBpbnRvIHNvbWV0aGluZyBhbWF6aW5nLlwiLFxuICAgICAgXCLwn6epIFRoZSBwaWVjZXMgYXJlIGNvbWluZyB0b2dldGhlciBwZXJmZWN0bHkuXCJcbiAgICBdLFxuICAgIHN5bnRoZXNpc19zdHJlYW1pbmc6IFtcbiAgICAgIFwi8J+ToSBTdHJlYW1pbmcgdGhlIGZpbmFsIHN5bnRoZXNpcyBsaXZlLi4uXCIsXG4gICAgICBcIuKaoSBSZWFsLXRpbWUgc3ludGhlc2lzIGluIHByb2dyZXNzIVwiLFxuICAgICAgXCLwn46sIFdhdGNoIHRoZSBtYWdpYyBoYXBwZW4gYXMgZXZlcnl0aGluZyBjb21iaW5lcy5cIlxuICAgIF0sXG4gICAgc3ludGhlc2lzX2NvbXBsZXRlOiBbXG4gICAgICBcIvCfjoogTWlzc2lvbiBhY2NvbXBsaXNoZWQhIFRoZSB0ZWFtIGhhcyBkZWxpdmVyZWQgYW4gb3V0c3RhbmRpbmcgcmVzdWx0LlwiLFxuICAgICAgXCLwn4+GIEluY3JlZGlibGUgdGVhbXdvcmshIFdlJ3ZlIGNyZWF0ZWQgc29tZXRoaW5nIHRydWx5IGV4Y2VwdGlvbmFsIHRvZ2V0aGVyLlwiLFxuICAgICAgXCLwn4yfIFBlcmZlY3QgZXhlY3V0aW9uISBUaGlzIGlzIHdoYXQgaGFwcGVucyB3aGVuIEFJIHNwZWNpYWxpc3RzIGNvbGxhYm9yYXRlIGJyaWxsaWFudGx5LlwiXG4gICAgXSxcbiAgICBvcmNoZXN0cmF0aW9uX2NvbXBsZXRlZDogW1xuICAgICAgXCLwn46JIEZ1bGwgb3JjaGVzdHJhdGlvbiBjb21wbGV0ZSEgV2hhdCBhbiBhbWF6aW5nIHRlYW0gZWZmb3J0LlwiLFxuICAgICAgXCLwn4+GIE1pc3Npb24gYWNjb21wbGlzaGVkISBUaGUgQUkgdGVhbSBkZWxpdmVyZWQgZXhjZWxsZW5jZS5cIixcbiAgICAgIFwi4pyoIE9yY2hlc3RyYXRpb24gZmluaXNoZWQgc3VjY2Vzc2Z1bGx5LiBPdXRzdGFuZGluZyBjb2xsYWJvcmF0aW9uIVwiXG4gICAgXSxcbiAgICBvcmNoZXN0cmF0aW9uX2ZhaWxlZDogW1xuICAgICAgXCLimqDvuI8gT3JjaGVzdHJhdGlvbiBlbmNvdW50ZXJlZCBpc3N1ZXMuIEFuYWx5emluZyBmb3IgcmVjb3ZlcnkuXCIsXG4gICAgICBcIvCflIQgVGVhbSBoaXQgc29tZSBjaGFsbGVuZ2VzLiBSZWdyb3VwaW5nIGZvciBuZXh0IGF0dGVtcHQuXCIsXG4gICAgICBcIvCfm6DvuI8gT3JjaGVzdHJhdGlvbiBuZWVkcyBhZGp1c3RtZW50LiBMZWFybmluZyBmcm9tIHRoaXMgZXhwZXJpZW5jZS5cIlxuICAgIF0sXG4gICAgbW9kZXJhdG9yX2NvbW1lbnRhcnk6IFtcbiAgICAgIFwi8J+Ome+4jyBNb2RlcmF0b3IgcHJvdmlkaW5nIGxpdmUgaW5zaWdodHMuLi5cIixcbiAgICAgIFwi8J+ToiBDb21tZW50YXJ5IHVwZGF0ZSBmcm9tIHRoZSBjb29yZGluYXRpb24gdGVhbS5cIixcbiAgICAgIFwi8J+OryBTdHJhdGVnaWMgZ3VpZGFuY2UgZnJvbSB0aGUgbW9kZXJhdG9yLlwiXG4gICAgXSxcbiAgICBzcGVjaWFsaXN0X21lc3NhZ2U6IFtcbiAgICAgIGDwn5KsICR7cm9sZUlkfSBoYXMgYW4gaW1wb3J0YW50IHVwZGF0ZSB0byBzaGFyZS5gLFxuICAgICAgYPCfk50gTWVzc2FnZSBmcm9tICR7cm9sZUlkfSBzcGVjaWFsaXN0LmAsXG4gICAgICBg8J+Xo++4jyAke3JvbGVJZH0gaXMgY29tbXVuaWNhdGluZyB3aXRoIHRoZSB0ZWFtLmBcbiAgICBdLFxuICAgIG1vZGVyYXRvcl9hc3NpZ25tZW50OiBbXG4gICAgICBg8J+OryBAJHtyb2xlSWR9LCB5b3UncmUgdXAhIFBsZWFzZSBiZWdpbiB5b3VyIHNwZWNpYWxpemVkIHdvcmsuYCxcbiAgICAgIGDwn5OLIEAke3JvbGVJZH0sIHRoaXMgaXMgcmlnaHQgaW4geW91ciB3aGVlbGhvdXNlLiBUYWtlIGl0IGF3YXkhYCxcbiAgICAgIGDimqEgQCR7cm9sZUlkfSwgSSBuZWVkIHlvdXIgZXhwZXJ0aXNlIGhlcmUuIFBsZWFzZSB3b3JrIHlvdXIgbWFnaWMuYFxuICAgIF0sXG4gICAgc3BlY2lhbGlzdF9hY2tub3dsZWRnbWVudDogW1xuICAgICAgYOKchSAke3JvbGVJZH0gYWNrbm93bGVkZ2VzIHRoZSBhc3NpZ25tZW50IGFuZCBpcyByZWFkeSB0byB3b3JrLmAsXG4gICAgICBg8J+OryAke3JvbGVJZH0gY29uZmlybXMgdW5kZXJzdGFuZGluZyBhbmQgYmVnaW5zIHRhc2sgZXhlY3V0aW9uLmAsXG4gICAgICBg4pqhICR7cm9sZUlkfSBpcyBsb2NrZWQgYW5kIGxvYWRlZCwgcmVhZHkgdG8gZGVsaXZlciBleGNlbGxlbmNlLmBcbiAgICBdLFxuICAgIGhhbmRvZmZfbWVzc2FnZTogW1xuICAgICAgYOKcqCBFeGNlbGxlbnQgd29yaywgQCR7cm9sZUlkfSEgUXVhbGl0eSBsb29rcyBncmVhdC4gUGFzc2luZyB0byBuZXh0IHNwZWNpYWxpc3QuLi5gLFxuICAgICAgYPCfkY8gT3V0c3RhbmRpbmcgZXhlY3V0aW9uLCBAJHtyb2xlSWR9ISBNb3ZpbmcgdG8gdGhlIG5leHQgcGhhc2UuLi5gLFxuICAgICAgYPCfjokgRmFudGFzdGljIHJlc3VsdHMsIEAke3JvbGVJZH0hIFRoZSB0ZWFtIGNvbGxhYm9yYXRpb24gY29udGludWVzLi4uYFxuICAgIF0sXG4gICAgY2xhcmlmaWNhdGlvbl9yZXF1ZXN0OiBbXG4gICAgICBg4p2TICR7cm9sZUlkfSBuZWVkcyBjbGFyaWZpY2F0aW9uIHRvIGRlbGl2ZXIgdGhlIGJlc3QgcmVzdWx0cy5gLFxuICAgICAgYPCfpJQgJHtyb2xlSWR9IGlzIGFza2luZyBmb3IgYWRkaXRpb25hbCBjb250ZXh0IHRvIG9wdGltaXplIHRoZWlyIHdvcmsuYCxcbiAgICAgIGDwn5KtICR7cm9sZUlkfSB3YW50cyB0byBlbnN1cmUgdGhleSB1bmRlcnN0YW5kIHRoZSByZXF1aXJlbWVudHMgcGVyZmVjdGx5LmBcbiAgICBdLFxuICAgIGNsYXJpZmljYXRpb25fcmVzcG9uc2U6IFtcbiAgICAgIGDwn5KhIENsYXJpZmljYXRpb24gcHJvdmlkZWQhICR7cm9sZUlkfSBjYW4gbm93IHByb2NlZWQgd2l0aCBjb25maWRlbmNlLmAsXG4gICAgICBg4pyFIFF1ZXN0aW9uIGFuc3dlcmVkISAke3JvbGVJZH0gaGFzIHRoZSBjb250ZXh0IHRoZXkgbmVlZC5gLFxuICAgICAgYPCfjq8gUGVyZmVjdCEgJHtyb2xlSWR9IG5vdyBoYXMgYWxsIHRoZSBkZXRhaWxzIGZvciBvcHRpbWFsIGV4ZWN1dGlvbi5gXG4gICAgXVxuICB9O1xuXG4gIGNvbnN0IG9wdGlvbnMgPSBjb21tZW50YXJpZXNbZXZlbnRUeXBlXSB8fCBbXCLwn6SWIFByb2Nlc3NpbmcuLi5cIl07XG4gIHJldHVybiBvcHRpb25zW01hdGguZmxvb3IoTWF0aC5yYW5kb20oKSAqIG9wdGlvbnMubGVuZ3RoKV07XG59XG5cbi8vIEdlbmVyYXRlIGNvbnZlcnNhdGlvbmFsIG1lc3NhZ2VzIGZvciBjaGF0cm9vbSBleHBlcmllbmNlXG5leHBvcnQgZnVuY3Rpb24gZ2VuZXJhdGVDb252ZXJzYXRpb25hbE1lc3NhZ2UoXG4gIGV2ZW50VHlwZTogT3JjaGVzdHJhdGlvbkV2ZW50VHlwZSxcbiAgcm9sZUlkPzogc3RyaW5nLFxuICBjb250ZXh0PzogYW55XG4pOiBzdHJpbmcge1xuICBjb25zdCBtZXNzYWdlczogUmVjb3JkPHN0cmluZywgc3RyaW5nW10+ID0ge1xuICAgIG9yY2hlc3RyYXRpb25fc3RhcnRlZDogW1xuICAgICAgXCLwn46sIFdlbGNvbWUgdG8gdGhlIEFJIFRlYW0gQ29sbGFib3JhdGlvbiEgSSdtIGFzc2VtYmxpbmcgdGhlIHBlcmZlY3QgdGVhbSBmb3IgdGhpcyB0YXNrLlwiLFxuICAgICAgXCLwn5qAIEFscmlnaHQgZXZlcnlvbmUsIHdlJ3ZlIGdvdCBhbiBleGNpdGluZyBjaGFsbGVuZ2UgYWhlYWQuIExldCBtZSBjb29yZGluYXRlIG91ciBzcGVjaWFsaXN0cy5cIixcbiAgICAgIFwi8J+OryBUaW1lIHRvIGJyaW5nIHRvZ2V0aGVyIG91ciBleHBlcnQgQUkgdGVhbSBmb3Igb3B0aW1hbCByZXN1bHRzIVwiXG4gICAgXSxcbiAgICB0YXNrX2RlY29tcG9zZWQ6IFtcbiAgICAgIFwi8J+TiyBJJ3ZlIGFuYWx5emVkIHRoZSB0YXNrIGFuZCBhc3NlbWJsZWQgdGhpcyBleHBlcnQgdGVhbS4gTGV0J3MgYmVnaW4gdGhlIGNvbGxhYm9yYXRpb24hXCIsXG4gICAgICBcIvCfjqogUGVyZmVjdCEgSSd2ZSBtYXRjaGVkIHRoZSByaWdodCBzcGVjaWFsaXN0cyB0byBlYWNoIHBhcnQgb2YgdGhpcyBjaGFsbGVuZ2UuXCIsXG4gICAgICBcIuKaoSBUYXNrIGJyZWFrZG93biBjb21wbGV0ZS4gT3VyIHRlYW0gaXMgcGVyZmVjdGx5IHBvc2l0aW9uZWQgZm9yIHN1Y2Nlc3MuXCJcbiAgICBdLFxuICAgIG1vZGVyYXRvcl9hc3NpZ25tZW50OiBbXG4gICAgICBg8J+OryBAJHtyb2xlSWR9LCB5b3UncmUgdXAhIFBsZWFzZSBiZWdpbiB5b3VyIHNwZWNpYWxpemVkIHdvcmsgb24gdGhpcyB0YXNrLmAsXG4gICAgICBg8J+TiyBAJHtyb2xlSWR9LCB0aGlzIGlzIHJpZ2h0IGluIHlvdXIgd2hlZWxob3VzZS4gVGFrZSBpdCBhd2F5IWAsXG4gICAgICBg4pqhIEAke3JvbGVJZH0sIEkgbmVlZCB5b3VyIGV4cGVydGlzZSBoZXJlLiBQbGVhc2Ugd29yayB5b3VyIG1hZ2ljLmBcbiAgICBdLFxuICAgIHNwZWNpYWxpc3RfYWNrbm93bGVkZ21lbnQ6IFtcbiAgICAgIGDinIUgVW5kZXJzdG9vZCEgSSdtICR7cm9sZUlkfSBhbmQgSSdsbCBoYW5kbGUgdGhpcyB0YXNrIHdpdGggZXhwZXJ0aXNlLiBTdGFydGluZyB3b3JrIG5vdy4uLmAsXG4gICAgICBg8J+OryBQZXJmZWN0ISBBcyB0aGUgJHtyb2xlSWR9IHNwZWNpYWxpc3QsIEknbSBleGNpdGVkIHRvIHRhY2tsZSB0aGlzIGNoYWxsZW5nZS5gLFxuICAgICAgYOKaoSBHb3QgaXQhICR7cm9sZUlkfSBoZXJlIC0gSSdsbCBkZWxpdmVyIGV4Y2VsbGVudCByZXN1bHRzIGZvciB0aGUgdGVhbS5gXG4gICAgXSxcbiAgICBzcGVjaWFsaXN0X21lc3NhZ2U6IFtcbiAgICAgIGDwn46JIEV4Y2VsbGVudCEgSSd2ZSBjb21wbGV0ZWQgbXkgcGFydCBvZiB0aGUgdGFzay4gSGVyZSdzIHdoYXQgSSd2ZSBkZWxpdmVyZWQ6YCxcbiAgICAgIGDinKggUGVyZmVjdCEgTXkgc3BlY2lhbGl6ZWQgd29yayBpcyBjb21wbGV0ZS4gVGFrZSBhIGxvb2sgYXQgdGhlIHJlc3VsdHM6YCxcbiAgICAgIGDwn5qAIE1pc3Npb24gYWNjb21wbGlzaGVkISBIZXJlJ3MgbXkgY29udHJpYnV0aW9uIHRvIHRoZSB0ZWFtIGVmZm9ydDpgXG4gICAgXSxcbiAgICBoYW5kb2ZmX21lc3NhZ2U6IFtcbiAgICAgIGDinKggRXhjZWxsZW50IHdvcmssIEAke3JvbGVJZH0hIFF1YWxpdHkgbG9va3MgZ3JlYXQuIE5vdyBwYXNzaW5nIHRvIHRoZSBuZXh0IHNwZWNpYWxpc3QuLi5gLFxuICAgICAgYPCfkY8gT3V0c3RhbmRpbmcgZXhlY3V0aW9uLCBAJHtyb2xlSWR9ISBNb3ZpbmcgdG8gdGhlIG5leHQgcGhhc2UuLi5gLFxuICAgICAgYPCfjokgRmFudGFzdGljIHJlc3VsdHMsIEAke3JvbGVJZH0hIFRoZSB0ZWFtIGNvbGxhYm9yYXRpb24gY29udGludWVzLi4uYFxuICAgIF0sXG4gICAgc3ludGhlc2lzX3N0YXJ0ZWQ6IFtcbiAgICAgIFwi8J+nqSBGYW50YXN0aWMgdGVhbXdvcmsgZXZlcnlvbmUhIE5vdyBJJ2xsIHN5bnRoZXNpemUgYWxsIHlvdXIgZXhjZWxsZW50IGNvbnRyaWJ1dGlvbnMgaW50byB0aGUgZmluYWwgZGVsaXZlcmFibGUuLi5cIixcbiAgICAgIFwi8J+OqCBBbWF6aW5nIGNvbGxhYm9yYXRpb24hIFRpbWUgdG8gd2VhdmUgYWxsIHRoZXNlIGJyaWxsaWFudCBvdXRwdXRzIHRvZ2V0aGVyLi4uXCIsXG4gICAgICBcIuKcqCBPdXRzdGFuZGluZyB3b3JrIHRlYW0hIExldCBtZSBjb21iaW5lIGV2ZXJ5dGhpbmcgaW50byB0aGUgcGVyZmVjdCBmaW5hbCByZXN1bHQuLi5cIlxuICAgIF0sXG4gICAgc3ludGhlc2lzX2NvbXBsZXRlOiBbXG4gICAgICBcIvCfjoogTWlzc2lvbiBhY2NvbXBsaXNoZWQhIFRoZSB0ZWFtIGhhcyBkZWxpdmVyZWQgYW4gb3V0c3RhbmRpbmcgcmVzdWx0LiBHcmVhdCBjb2xsYWJvcmF0aW9uIGV2ZXJ5b25lIVwiLFxuICAgICAgXCLwn4+GIEluY3JlZGlibGUgdGVhbXdvcmshIFdlJ3ZlIGNyZWF0ZWQgc29tZXRoaW5nIHRydWx5IGV4Y2VwdGlvbmFsIHRvZ2V0aGVyLlwiLFxuICAgICAgXCLwn4yfIFBlcmZlY3QgZXhlY3V0aW9uISBUaGlzIGlzIHdoYXQgaGFwcGVucyB3aGVuIEFJIHNwZWNpYWxpc3RzIGNvbGxhYm9yYXRlIGJyaWxsaWFudGx5LlwiXG4gICAgXVxuICB9O1xuXG4gIGNvbnN0IG9wdGlvbnMgPSBtZXNzYWdlc1tldmVudFR5cGVdIHx8IFtcIvCfpJYgUHJvY2Vzc2luZy4uLlwiXTtcbiAgY29uc3Qgc2VsZWN0ZWRNZXNzYWdlID0gb3B0aW9uc1tNYXRoLmZsb29yKE1hdGgucmFuZG9tKCkgKiBvcHRpb25zLmxlbmd0aCldO1xuXG4gIC8vIFJlcGxhY2UgcGxhY2Vob2xkZXJzIHdpdGggY29udGV4dCBpZiBhdmFpbGFibGVcbiAgaWYgKGNvbnRleHQgJiYgdHlwZW9mIHNlbGVjdGVkTWVzc2FnZSA9PT0gJ3N0cmluZycpIHtcbiAgICByZXR1cm4gc2VsZWN0ZWRNZXNzYWdlLnJlcGxhY2UoL1xceyhcXHcrKVxcfS9nLCAobWF0Y2gsIGtleSkgPT4gY29udGV4dFtrZXldIHx8IG1hdGNoKTtcbiAgfVxuXG4gIHJldHVybiBzZWxlY3RlZE1lc3NhZ2U7XG59XG5cbi8vIENhbGN1bGF0ZSBvcHRpbWFsIGV4ZWN1dGlvbiBzdHJhdGVneSAocGFyYWxsZWwgdnMgc2VxdWVudGlhbClcbmV4cG9ydCBmdW5jdGlvbiBjYWxjdWxhdGVFeGVjdXRpb25TdHJhdGVneSh3b3JrZmxvdzogT3JjaGVzdHJhdGlvbldvcmtmbG93KToge1xuICBzdHJhdGVneTogJ3NlcXVlbnRpYWwnIHwgJ3BhcmFsbGVsJyB8ICdoeWJyaWQnO1xuICBlc3RpbWF0ZWRTcGVlZHVwOiBudW1iZXI7XG4gIHJpc2tMZXZlbDogJ2xvdycgfCAnbWVkaXVtJyB8ICdoaWdoJztcbn0ge1xuICBjb25zdCB0b3RhbFN0ZXBzID0gd29ya2Zsb3cuc3RlcHMubGVuZ3RoO1xuICBjb25zdCBwYXJhbGxlbEdyb3VwcyA9IHdvcmtmbG93LnBhcmFsbGVsR3JvdXBzLmxlbmd0aDtcbiAgXG4gIGlmIChwYXJhbGxlbEdyb3VwcyA9PT0gdG90YWxTdGVwcykge1xuICAgIHJldHVybiB7XG4gICAgICBzdHJhdGVneTogJ3NlcXVlbnRpYWwnLFxuICAgICAgZXN0aW1hdGVkU3BlZWR1cDogMS4wLFxuICAgICAgcmlza0xldmVsOiAnbG93J1xuICAgIH07XG4gIH1cbiAgXG4gIGlmIChwYXJhbGxlbEdyb3VwcyA9PT0gMSkge1xuICAgIHJldHVybiB7XG4gICAgICBzdHJhdGVneTogJ3BhcmFsbGVsJyxcbiAgICAgIGVzdGltYXRlZFNwZWVkdXA6IHRvdGFsU3RlcHMgKiAwLjcsIC8vIEFzc3VtZSA3MCUgZWZmaWNpZW5jeSBpbiBwYXJhbGxlbFxuICAgICAgcmlza0xldmVsOiAnaGlnaCdcbiAgICB9O1xuICB9XG4gIFxuICByZXR1cm4ge1xuICAgIHN0cmF0ZWd5OiAnaHlicmlkJyxcbiAgICBlc3RpbWF0ZWRTcGVlZHVwOiAodG90YWxTdGVwcyAvIHBhcmFsbGVsR3JvdXBzKSAqIDAuOCwgLy8gODAlIGVmZmljaWVuY3kgZm9yIGh5YnJpZFxuICAgIHJpc2tMZXZlbDogJ21lZGl1bSdcbiAgfTtcbn1cbiJdLCJuYW1lcyI6WyJjcmVhdGVPcmNoZXN0cmF0aW9uRXZlbnRTdHJlYW0iLCJleGVjdXRpb25JZCIsImVuY29kZXIiLCJUZXh0RW5jb2RlciIsIlJlYWRhYmxlU3RyZWFtIiwic3RhcnQiLCJjb250cm9sbGVyIiwiaW5pdGlhbEV2ZW50IiwiaWQiLCJjcnlwdG8iLCJyYW5kb21VVUlEIiwiZXhlY3V0aW9uX2lkIiwidHlwZSIsInRpbWVzdGFtcCIsIkRhdGUiLCJ0b0lTT1N0cmluZyIsImRhdGEiLCJtZXNzYWdlIiwiZXZlbnREYXRhIiwiSlNPTiIsInN0cmluZ2lmeSIsImVucXVldWUiLCJlbmNvZGUiLCJjYW5jZWwiLCJjb25zb2xlIiwibG9nIiwiZGVjb21wb3NlVGFza1dpdGhEZXBlbmRlbmNpZXMiLCJvcmlnaW5hbFByb21wdCIsInJvbGVzIiwiY2xhc3NpZmljYXRpb25BcGlLZXkiLCJkZWNvbXBvc2l0aW9uUHJvbXB0IiwibWFwIiwiciIsInJvbGVJZCIsImNvbmZpZGVuY2UiLCJqb2luIiwicmVzcG9uc2UiLCJmZXRjaCIsIm1ldGhvZCIsImhlYWRlcnMiLCJib2R5IiwibW9kZWwiLCJtZXNzYWdlcyIsInJvbGUiLCJjb250ZW50IiwidGVtcGVyYXR1cmUiLCJtYXhfdG9rZW5zIiwicmVzcG9uc2VfZm9ybWF0Iiwib2siLCJFcnJvciIsInN0YXR1cyIsInJlc3VsdCIsImpzb24iLCJjaG9pY2VzIiwicGFyc2VkIiwicGFyc2UiLCJ3b3JrZmxvdyIsImVycm9yIiwid2FybiIsImNyZWF0ZUZhbGxiYWNrV29ya2Zsb3ciLCJzb3J0ZWRSb2xlcyIsInNvcnQiLCJhIiwiYiIsImV4ZWN1dGlvbk9yZGVyIiwic3RlcHMiLCJpbmRleCIsInN0ZXBOdW1iZXIiLCJwcm9tcHQiLCJkZXBlbmRlbmNpZXMiLCJjYW5SdW5JblBhcmFsbGVsIiwicHJpb3JpdHkiLCJlc3RpbWF0ZWREdXJhdGlvbiIsIm1vZGVyYXRvckluc3RydWN0aW9ucyIsInBhcmFsbGVsR3JvdXBzIiwiXyIsInRvdGFsRXN0aW1hdGVkRHVyYXRpb24iLCJsZW5ndGgiLCJjb21wbGV4aXR5U2NvcmUiLCJNYXRoIiwibWluIiwiYWN0aXZlU3RyZWFtcyIsIk1hcCIsImJyb2FkY2FzdE9yY2hlc3RyYXRpb25FdmVudCIsImV2ZW50Iiwic3RyZWFtSW5mbyIsImdldCIsImRlc2lyZWRTaXplIiwibGFzdEV2ZW50SWQiLCJkZWxldGUiLCJUeXBlRXJyb3IiLCJpbmNsdWRlcyIsInJlZ2lzdGVyU3RyZWFtQ29udHJvbGxlciIsInNldCIsInN0YXJ0VGltZSIsIm5vdyIsInVucmVnaXN0ZXJTdHJlYW1Db250cm9sbGVyIiwiZ2V0QWN0aXZlU3RyZWFtQ291bnQiLCJzaXplIiwiY2xlYW51cE9sZFN0cmVhbXMiLCJtYXhBZ2VNcyIsImVudHJpZXMiLCJjbG9zZSIsImVtaXRPcmNoZXN0cmF0aW9uRXZlbnQiLCJldmVudFR5cGUiLCJtb2RlbE5hbWUiLCJzdGVwX251bWJlciIsInJvbGVfaWQiLCJtb2RlbF9uYW1lIiwiZ2VuZXJhdGVNb2RlcmF0b3JDb21tZW50YXJ5Iiwic3RlcERhdGEiLCJjb21tZW50YXJpZXMiLCJvcmNoZXN0cmF0aW9uX3N0YXJ0ZWQiLCJ0YXNrX2RlY29tcG9zZWQiLCJzdGVwX2Fzc2lnbmVkIiwic3RlcF9zdGFydGVkIiwic3RlcF9wcm9ncmVzcyIsInN0ZXBfc3RyZWFtaW5nIiwic3RlcF9jb21wbGV0ZWQiLCJzdGVwX2ZhaWxlZCIsInN5bnRoZXNpc19zdGFydGVkIiwic3ludGhlc2lzX3Byb2dyZXNzIiwic3ludGhlc2lzX3N0cmVhbWluZyIsInN5bnRoZXNpc19jb21wbGV0ZSIsIm9yY2hlc3RyYXRpb25fY29tcGxldGVkIiwib3JjaGVzdHJhdGlvbl9mYWlsZWQiLCJtb2RlcmF0b3JfY29tbWVudGFyeSIsInNwZWNpYWxpc3RfbWVzc2FnZSIsIm1vZGVyYXRvcl9hc3NpZ25tZW50Iiwic3BlY2lhbGlzdF9hY2tub3dsZWRnbWVudCIsImhhbmRvZmZfbWVzc2FnZSIsImNsYXJpZmljYXRpb25fcmVxdWVzdCIsImNsYXJpZmljYXRpb25fcmVzcG9uc2UiLCJvcHRpb25zIiwiZmxvb3IiLCJyYW5kb20iLCJnZW5lcmF0ZUNvbnZlcnNhdGlvbmFsTWVzc2FnZSIsImNvbnRleHQiLCJzZWxlY3RlZE1lc3NhZ2UiLCJyZXBsYWNlIiwibWF0Y2giLCJrZXkiLCJjYWxjdWxhdGVFeGVjdXRpb25TdHJhdGVneSIsInRvdGFsU3RlcHMiLCJzdHJhdGVneSIsImVzdGltYXRlZFNwZWVkdXAiLCJyaXNrTGV2ZWwiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/utils/orchestrationUtils.ts\n");

/***/ })

};
;